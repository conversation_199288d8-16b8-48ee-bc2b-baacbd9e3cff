package com.easylive.mgs.provider.pub.cdn;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
public class TencentURLSignerFactory {
    public static URLSigner typeD() {
        return (signKey, path, expireTime) -> {
            final String sign = DigestUtils.md5Hex(signKey + path + expireTime);
            return String.format("%s?t=%d&sign=%s", path, expireTime, sign);
        };
    }

    public static URLSigner typeLive() {
        return (signKey, path, expireTime) -> {
            String txTime = Long.toHexString(expireTime).toUpperCase();
            // parse path to stream name
            //提取AppName和StreamName
            String[] parts = path.split("/");
            String streamName = "";
            if( parts.length >= 3 ) {
                streamName = parts[2];
                if( streamName.contains(".")) {
                    streamName = streamName.split("\\.")[0];
                }
            }

            String input = signKey +
                    streamName +
                    Long.toHexString(expireTime).toUpperCase();

            String txSecret = DigestUtils.md5Hex(input).toLowerCase();
            return path + "?txSecret=" + txSecret + "&" + "txTime=" + txTime;
        };
    }
}

