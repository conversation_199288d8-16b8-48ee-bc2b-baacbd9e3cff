package com.easylive.mgs.provider.pub.enums;

/**
 * 图片检测返回模型
 *
 * <AUTHOR>
 * @date 2020/6/1
 */
public enum SceneType {

    /**
     * porn: 色情
     * terrorism: 暴恐
     * qrcode: 二维码
     * ad: 图片广告
     * ocr: 文字识别
     */
    PORN("porn", "色情"),
    TERRORISM("terrorism", "暴恐"),
    QRCODE("qrcode", "二维码"),
    AD("ad", "图片广告"),
    OCR("ocr", "文字识别");

    private String value;

    private String message;

    SceneType(String value, String message) {
        this.value = value;
        this.message = message;
    }

    public String getValue() {
        return value;
    }

    public String getMessage() {
        return message;
    }

}
