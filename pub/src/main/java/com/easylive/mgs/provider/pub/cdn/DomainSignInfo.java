package com.easylive.mgs.provider.pub.cdn;

import com.easylive.mgs.provider.pub.enums.CDNDomainType;
import com.easylive.mgs.provider.pub.enums.CDNSignType;
import com.easylive.mgs.provider.pub.enums.CloudProviderType;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/2
 */
@Data
public class DomainSignInfo {
    private CloudProviderType providerType;
    private CDNSignType signType;
    private String signKey;
    private CDNDomainType domainType;
}
