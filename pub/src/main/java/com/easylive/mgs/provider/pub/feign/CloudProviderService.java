package com.easylive.mgs.provider.pub.feign;

import com.easylive.mgs.provider.pub.model.CloudProviderAccessDTO;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient(value = "provider")
@Component
public interface CloudProviderService {
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/cloud/access/list")
    List<CloudProviderAccessDTO> getAllAccess();
}
