package com.easylive.mgs.provider.pub.stub;

import com.easylive.mgs.provider.pub.feign.WeixinAppService;

/**
 * <AUTHOR>
 * @date 2024/1/16
 */
public class WeixinAppServiceStub {
    private final WeixinAppService appService;
    private static final String DEFAULT_ENV = "release";
    private static final int DEFAULT_VALID_TIME = 1440;

    public WeixinAppServiceStub(WeixinAppService appService) {
        this.appService = appService;
    }

    public String generateAppletScheme(String appId, String path, String query) {
        //默认release， 1天
        return appService.generateAppletScheme(appId, path, query, DEFAULT_ENV, DEFAULT_VALID_TIME);
    }

    public String generateAppletScheme(String appId, String path, String query, int validTimeInMinutes) {
        //默认release
        return appService.generateAppletScheme(appId, path, query, DEFAULT_ENV, validTimeInMinutes);
    }
    public String generateAppletScheme(String appId, String path, String query, String env, int validTimeInMinutes) {
        return appService.generateAppletScheme(appId, path, query, env, validTimeInMinutes);
    }
}
