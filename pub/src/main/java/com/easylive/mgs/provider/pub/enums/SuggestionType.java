package com.easylive.mgs.provider.pub.enums;

/**
 * 检测返回模型
 *
 * <AUTHOR>
 * @date 2020/6/1
 */
public enum SuggestionType {

    /**
     * pass：正常，无需进行其余操作；或者未识别出目标对象
     * review：检测结果不确定，需要进行人工审核；或者识别出目标对象
     * block：违规，建议执行进一步操作（如直接删除或做限制处理）
     */
    PASS("pass", "正常"),
    REVIEW("review", "检测结果不确定，需要进行人工审核"),
    BLOCK("block", "违规");

    private String value;

    private String message;

    SuggestionType(String value, String message) {
        this.value = value;
        this.message = message;
    }

    public String getValue() {
        return value;
    }

    public String getMessage() {
        return message;
    }

    public static Integer ofOrdinal(String val) {
        for (SuggestionType suggestionType : SuggestionType.values()) {
            if (suggestionType.getValue().equals(val)) {
                return suggestionType.ordinal();
            }
        }

        return 0;
    }

}
