package com.easylive.mgs.provider.pub.enums;

/**
 * 广告检测返回模型
 * <AUTHOR>
 * @date 2020/6/1
 */
public enum AdLabelType {

    /**
     * normal：正常图片
     * politics：文字含涉政内容
     * porn：文字含涉黄内容
     * abuse：文字含辱骂内容
     * terrorism：文字含暴恐内容
     * contraband：文字含违禁内容
     * spam：文字含其他垃圾内容
     * npx：牛皮藓广告
     * qrcode：包含二维码
     * programCode：包含小程序码
     * ad：其他广告
     */
    NORMAL("normal", "正常图片，无色情内容"),
    AD("ad", "其他广告");

    private String value;

    private String message;

    AdLabelType(String value, String message) {
        this.value = value;
        this.message = message;
    }

    public String getValue() {
        return value;
    }

    public String getMessage() {
        return message;
    }

}
