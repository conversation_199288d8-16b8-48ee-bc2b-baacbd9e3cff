package com.easylive.mgs.provider.pub.cdn;

import org.apache.commons.codec.digest.DigestUtils;

import java.text.SimpleDateFormat;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
public class HuoshanURLSignerFactory {
    private final static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");


    /**
     * 火山引擎 CDN URL 鉴权 B 类型
     * <a href="https://www.volcengine.com/docs/6454/1129833">B 类型配置文档</a>
     * 
     * 签名计算公式: MD5(key + timestamp + uri)
     * URL 格式: scheme://host/timestamp/<SIGNATURE>/uri(?query_params)
     * timestamp 格式: YYYYMMDDHHMM
     * 
     * @return 鉴权后的路径
     */
    public static URLSigner typeB() {
        return (signKey, path, expireTime) -> {
            //将expireTime格式化为YYYYMMDDHHMM格式
            String timestamp = dateFormat.format(expireTime*1000);
            String orgString = signKey + timestamp + path;
            String md5Hash = DigestUtils.md5Hex(orgString);
            return "/" + md5Hash + "/" + timestamp + path;
        };
    }

    /**
     * 火山引擎 CDN URL 鉴权 C 类型
     * <a href="https://www.volcengine.com/docs/6454/1129834">C 类型配置文档</a>
     * 
     * 签名计算公式: MD5(key + uri + timestamp)
     * URL 格式: scheme://host/<signature>/timestamp/uri(?query_params)
     * timestamp 格式: 十六进制的 Unix 时间戳
     * 
     * @return 鉴权后的路径
     */
    public static URLSigner typeC() {
        return (signKey, path, expireTime) -> {
            String hexString = Long.toHexString(expireTime);
            String orgString = signKey + path + hexString;
            String md5Hash = DigestUtils.md5Hex(orgString);
            return "/" + md5Hash + "/" + hexString + path;
        };
    }

    /***
     * 火山直播鉴权
     * <a href="https://www.volcengine.com/docs/6469/107759#%E9%89%B4%E6%9D%83%E4%BF%A1%E6%81%AF%E7%BB%84%E6%88%90%E8%AF%B4%E6%98%8E">...</a>
     * volcTime = {UnixTime}
     * volcSecret = MD5("/{AppName}/{StreamName}{SecretKey}{volcTime}")
     * @return 鉴权后的路径
     */
    public static URLSigner typeLive() {
        return (signKey, path, expireTime) -> {
            String unixTime = Long.toString(expireTime);
            //提取AppName和StreamName
            String[] parts = path.split("/");
            String appName = "";
            String streamName = "";
            if( parts.length >= 3 ) {
                appName = parts[1];
                streamName = parts[2];
                if( streamName.contains(".")) {
                    streamName = streamName.split("\\.")[0];
                }
            }

            String input = String.format("/%s/%s%s%s",appName,streamName, signKey, unixTime);
            String signed = DigestUtils.md5Hex(input).toLowerCase();
            return path + "?volcTime=" + unixTime + "&volcSecret=" + signed;
        };
    }

}
