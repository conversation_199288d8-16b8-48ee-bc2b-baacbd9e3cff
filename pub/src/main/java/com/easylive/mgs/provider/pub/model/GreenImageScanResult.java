package com.easylive.mgs.provider.pub.model;

/**
 * 名称	类型	是否必须	描述
 * scene	字符串	是	图片检测场景，和调用请求中的场景（scenes）对应。
 * label	字符串	是	检测结果的分类，与具体的scene对应。取值范围参见scene和label说明。
 * suggestion	字符串	是	建议您执行的操作，取值范围：
 * pass：图片正常，无需进行其余操作；或者未识别出目标对象
 * review：检测结果不确定，需要进行人工审核；或者识别出目标对象
 * block：图片违规，建议执行进一步操作（如直接删除或做限制处理）
 * rate	浮点数	是	结果为该分类的概率，取值范围为[0.00-100.00]。值越高，表示越有可能属于该分类。
 * frames	数组	否	如果待检测图片因为过长被截断，该参数返回截断后的每一帧图像的临时访问地址，供您参考。具体结构描述见表 3。
 * hintWordsInfo	数组	否	图片中含有广告时，返回图片中广告文字命中的风险关键词信息。格式为数组，具体结构描述见hintWordsInfo。
 * 说明 仅适用于ad场景。
 * 示例值：
 * "hintWordsInfo":[{"context":"敏感词"}]
 * qrcodeData	字符串数组	否	图片中含有二维码时，返回图片中所有二维码包含的文本信息。
 * 说明 仅适用于qrcode场景。
 * programCodeData	数组	否	图片中含有小程序码时，返回小程序码的位置信息，具体结构描述见programCodeData。
 * 说明 仅适用于qrcode场景，且已通过工单联系我们开通了小程序码识别。
 * logoData	数组	否	图片中含有logo时，返回识别出来的logo信息，具体结构描述见logoData。
 * 说明 仅适用于logo场景。
 * sfaceData	数组	否	图片中包含暴恐识涉政内容时，返回识别出来的暴恐涉政信息，具体结构描述见sfaceData。
 * 说明 仅适用于terrorism和sface场景。关于该参数在sface场景中的具体内容，请参见敏感人脸检测。
 * ocrData	字符串数组	否	识别到的图片中的完整文字信息。
 * 说明 默认不返回，如需返回请通过工单联系我们。
 */

/**
 * 图片检测返回模型
 *
 * <AUTHOR>
 * @date 2020/6/1
 */
public class GreenImageScanResult {

    /**
     * 图片检测场景，和调用请求中的场景（scenes）对应。
     */
    private String scene;

    /**
     * 检测结果的分类，与具体的scene对应。取值范围参见scene和label说明。
     */
    private String label;

    /**
     * pass：图片正常，无需进行其余操作；或者未识别出目标对象
     * review：检测结果不确定，需要进行人工审核；或者识别出目标对象
     * block：图片违规，建议执行进一步操作（如直接删除或做限制处理）
     */
    private String suggestion;

    /**
     * 结果为该分类的概率，取值范围为[0.00-100.00]。值越高，表示越有可能属于该分类。
     */
    private float rate;

    /**
     * 业务数据我唯一标识
     */
    private String dataId;

    public GreenImageScanResult() {
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public float getRate() {
        return rate;
    }

    public void setRate(float rate) {
        this.rate = rate;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }
}
