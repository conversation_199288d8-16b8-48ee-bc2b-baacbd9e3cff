package com.easylive.mgs.provider.pub.cdn;

import com.easylive.mgs.provider.pub.util.UrlUtil;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * 七牛云 CDN 时间戳防盗链签名工厂
 *
 * <AUTHOR>
 * @date 2024/6/13
 */
public class QiniuURLSignerFactory {
    /**
     * 七牛云时间戳防盗链签名
     * 文档：<a href="https://developer.qiniu.com/fusion/kb/1670/timestamp-hotlinking-prevention">...</a>
     * 签名格式：/<原始资源路径>?<原始查询参数>sign=<签名串>&t=<时间戳>
     * 签名串计算方式：sign = md5Hex( <url_path>?<query_params> + <secret_key> + <timestamp> )
     * 其中 <timestamp> 是十六进制的 Unix 时间戳（秒），<secret_key> 是七牛后台配置的密钥。
     *
     * @return 鉴权后的路径
     */
    public static URLSigner typeDefault() {
        return (signKey, path, expireTime) -> {
            // expireTime: unix时间戳（秒）
            String hexTimestamp = Long.toHexString(expireTime);
            String urlPath = path;
            String query = "";
            // 拆分 path 和 query
            int idx = path.indexOf('?');
            if (idx >= 0) {
                urlPath = path.substring(0, idx);
                query = path.substring(idx + 1);
            }
            // 正确的path编码
            String encodedPath = UrlUtil.encodePath(urlPath);
            // 拼接签名字符串 S = key + encodePath(path) + T
            String sign = DigestUtils.md5Hex(signKey + encodedPath + hexTimestamp);
            // 拼接最终URL
            StringBuilder result = new StringBuilder();
            result.append(encodedPath);
            if (!query.isEmpty()) {
                result.append("?").append(query).append("&");
            } else {
                result.append("?");
            }
            result.append("sign=").append(sign).append("&t=").append(hexTimestamp);
            return result.toString();
        };
    }
}