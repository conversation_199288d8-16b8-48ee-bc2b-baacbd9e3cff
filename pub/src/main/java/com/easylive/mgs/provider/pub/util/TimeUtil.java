package com.easylive.mgs.provider.pub.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Slf4j
public class TimeUtil {


    private static final DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("HH:mm");

    public static LocalTime parseTime(String s) {
        if (StringUtils.isBlank(s)) {
            return null;
        }
        s = s.trim();
        return LocalTime.parse(s, dateFormat);
    }

    // 支持以下格式：
    // 20:00-21:00,15:00-17:00  (多个时间区间，逗号分隔)
    // 13:00-15:00, 20:00-      (右边为空表示到当天结束)
// -18:00, 19:00-           (左边为空表示从当天开始)
    public static List<Pair<LocalTime, LocalTime>> parseTimeRange(String time) {
        List<Pair<LocalTime, LocalTime>> ret = new ArrayList<>();
        if (StringUtils.isBlank(time)) {
            return ret;
        }

        try {
            String[] timeRanges = time.split(",");
            for (String timeRange : timeRanges) {
                timeRange = timeRange.trim();
                if (StringUtils.isBlank(timeRange) || !timeRange.contains("-")) {
                    continue;
                }

                String[] parts = timeRange.split("-");
                LocalTime startTime = null;
                LocalTime endTime = null;

                // 处理左边时间
                if (parts.length > 0 && StringUtils.isNotBlank(parts[0])) {
                    try {
                        startTime = parseTime(parts[0]);
                    } catch (Exception e) {
                        log.warn("Invalid start time format: {}", parts[0]);
                        continue;
                    }
                } else {
                    startTime = LocalTime.MIN; // 00:00
                }

                // 处理右边时间
                if (parts.length > 1 && StringUtils.isNotBlank(parts[1])) {
                    try {
                        endTime = parseTime(parts[1]);
                    } catch (Exception e) {
                        log.warn("Invalid end time format: {}", parts[1]);
                        continue;
                    }
                } else {
                    endTime = LocalTime.MAX; // 23:59:59.999999999
                }

                // 验证时间区间有效性
                if (startTime != null && endTime != null && !endTime.isBefore(startTime)) {
                    ret.add(Pair.of(startTime, endTime));
                }
            }
        } catch (Exception e) {
            log.error("Failed to parse valid time: {}", time, e);
        }

        return ret;
    }
}
