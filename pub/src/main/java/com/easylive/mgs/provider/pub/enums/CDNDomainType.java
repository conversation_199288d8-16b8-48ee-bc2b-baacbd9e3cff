package com.easylive.mgs.provider.pub.enums;

/**
 * <AUTHOR>
 * @date 2025/3/13
 */
public enum CDNDomainType {

    CDN("cdn", 0),
    PULL("pull", 1),
    PUSH("push", 2);

    private final String name;
    private final int value;

    CDNDomainType(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public static CDNDomainType fromValue(int value) {
        for (CDNDomainType type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public int getValue() {
        return value;
    }
}
