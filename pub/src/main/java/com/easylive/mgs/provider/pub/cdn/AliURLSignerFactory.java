package com.easylive.mgs.provider.pub.cdn;

import org.apache.commons.codec.digest.DigestUtils;

import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
public class AliURLSignerFactory {
    private final static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmm");
    public static URLSigner typeB() {
        return (signKey, path, expireTime) -> {
            //将expireTime格式化为YYYYMMDDHHMM格式
            String timestamp = dateFormat.format(expireTime*1000);
            String orgString = signKey + timestamp + path;
            String md5Hash = DigestUtils.md5Hex(orgString);
            return "/" + md5Hash + "/" + timestamp + path;
        };
    }

    public static URLSigner typeC() {
        return (signKey, path, expireTime) -> {
            String hexString = Long.toHexString(expireTime);
            String orgString = signKey + path + hexString;
            String md5Hash = DigestUtils.md5Hex(orgString);
            return "/" + md5Hash + "/" + hexString + path;
        };
    }

    public static URLSigner typeLive() {
        return (signKey, path, expireTime) -> {
            String unixTime = Long.toString(expireTime);
            String input = String.format("%s-%s-0-0-%s",path, unixTime, signKey);
            String signed = DigestUtils.md5Hex(input).toLowerCase();
            return "auth_key=" + signed;
        };
    }
}
