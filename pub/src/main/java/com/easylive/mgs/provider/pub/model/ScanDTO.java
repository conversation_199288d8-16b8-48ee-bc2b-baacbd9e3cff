package com.easylive.mgs.provider.pub.model;


import com.easylive.mgs.provider.pub.enums.DetectionLabelsEnum;
import com.easylive.mgs.provider.pub.enums.SuggestionType;

public class ScanDTO {

    /**
     * 检测到场景，和调用请求中的场景（scenes）对应。
     */
    private String scene;

    /**
     * @see DetectionLabelsEnum
     * 检测结果的分类，与具体的scene对应。
     */
    private String labels;

    /**
     * pass：正常，无需进行其余操作；或者未识别出目标对象
     * review：检测结果不确定，需要进行人工审核；或者识别出目标对象
     * block：违规，建议执行进一步操作（如直接删除或做限制处理）
     */
    private SuggestionType suggestion;

    /**
     * 结果为该分类的概率，取值范围为[0.00-100.00]。值越高，表示越有可能属于该分类。
     */
    private float rate;

    /**
     * 风险提示，多个可以以逗号隔开
     */
    private String tips;

    public ScanDTO() {
    }

    public ScanDTO(String scene, SuggestionType suggestion, float rate) {
        this.scene = scene;
        this.suggestion = suggestion;
        this.rate = rate;
        this.tips = "";
        this.labels = "";
    }

    public ScanDTO(String labels, SuggestionType suggestion, float rate, String tips) {
        this.labels = labels;
        this.suggestion = suggestion;
        this.rate = rate;
        this.tips = tips;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public String getLabels() {
        return labels;
    }

    public void setLabels(String labels) {
        this.labels = labels;
    }

    public float getRate() {
        return rate;
    }

    public void setRate(float rate) {
        this.rate = rate;
    }

    public SuggestionType getSuggestion() {
        return suggestion;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public void setSuggestion(SuggestionType suggestion) {
        this.suggestion = suggestion;
    }

    @Override
    public String toString() {
        return "ScanDTO{" +
                "scene='" + scene + '\'' +
                ", labels=" + labels +
                ", suggestion=" + suggestion +
                ", rate=" + rate +
                ", tips='" + tips + '\'' +
                '}';
    }
}
