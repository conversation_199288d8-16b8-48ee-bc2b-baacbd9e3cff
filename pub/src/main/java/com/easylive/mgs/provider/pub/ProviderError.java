package com.easylive.mgs.provider.pub;

import com.easylive.rpc.http.ResponseError;
import org.springframework.http.HttpStatus;

/**
 * 本服务业务错误码
 */
public enum ProviderError implements ResponseError {


    /**
     * 业务错误码，从一个范围开始，每个范围1000个错误码，22000 - 22999
     */
    E_PROVIDER(22000),

    /**
     * 位置错误码 22010  -  22030
     */
    E_GPS_PARAM(22010),
    E_GPS_BAIDU(22011),
    E_IP(22012),

    E_TENCENT_TASK_EXISTS(22014),
    E_TENCENT_CREATE(22015),
    E_TENCENT_CANCEL(22016),
    E_TENCENT_DETAIL(22017),
    E_TENCENT_TASK_NOT_EXISTS(22018),
    E_TENCENT_TASK_LIMIT(22019),
    E_ALI_SCAN_TASK_LIMIT(22020),
    E_ALI_TASK_EXISTS(22021),
    E_ALI_CREATE(22022),
    E_ALI_SIGN(22023),


    E_WEIXIN(22030),
    E_WEIXIN_APP_NOT_EXISTS(22031),
    E_WEIXIN_SERVICE(22032),
    E_WEIXIN_ACCESS_TOKEN_INVALID(22033),
    E_WEIXIN_APPLET_INVALID_EXPIRE_TIME(22034),


    E_CDN(22040),
    E_CDN_INVALID_REFRESH_TYPE(22041),
    E_CDN_DOMAIN_NOT_EXISTS(22042),
    E_CDN_PROVIDER_NOT_SUPPORT(22043),
    E_CDN_SIGN(22044),
    E_CDN_REFRESH(22045),
    ;


    private final int code;
    private HttpStatus status;

    ProviderError(final int code) {
        this.code = code;
        status = HttpStatus.UNPROCESSABLE_ENTITY;
    }

    ProviderError(final int value, HttpStatus status) {
        this.code = value;
        this.status = status;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return status;
    }

    @Override
    public String getName() {
        return null;
    }

    @Override
    public String getMessage() {
        return null;
    }
}
