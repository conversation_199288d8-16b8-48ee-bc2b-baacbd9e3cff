package com.easylive.mgs.provider.pub.enums;

/**
 * 云厂商枚举类型
 * <AUTHOR>
 * @date 2024/6/26
 */
public enum CloudProviderType {
    ALI("ali", 1),
    TENCENT("tencent", 2),
    HUOSHAN("huoshan", 3),
    QINIU("qiniu", 4),
    AWS("aws", 5),
    BAIDU("baidu", 6);

    private final String name;
    private final int value;

    private CloudProviderType(String name, int value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public int getValue() {
        return value;
    }

    public static CloudProviderType fromName(String name) {
        if(  name == null )
            return null;
        name = name.toLowerCase();

        for (CloudProviderType cloudProviderType : CloudProviderType.values()) {
            if (cloudProviderType.getName().toLowerCase().equals(name)) {
                return cloudProviderType;
            }
        }
        return null;
    }

    public static CloudProviderType fromValue(int value) {
        for (CloudProviderType cloudProviderType : CloudProviderType.values()) {
            if (cloudProviderType.getValue() == value) {
                return cloudProviderType;
            }
        }
        return null;
    }
}
