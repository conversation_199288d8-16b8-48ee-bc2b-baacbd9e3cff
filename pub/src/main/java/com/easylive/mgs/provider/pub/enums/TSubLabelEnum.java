package com.easylive.mgs.provider.pub.enums;

/**
 * 腾讯云二级label
 *
 * <AUTHOR>
 * @date 2020/6/1
 */
public enum TSubLabelEnum {
    /**
     * 二级label名称，描述
     */
    WomenSexy(TLabelEnum.Sexy,"WomenSexy", "女性性感着装"),
    MenSexy(TLabelEnum.Sexy,"MenSexy", "男性性感着装"),
    WomenSexyBack(TLabelEnum.Sexy,"WomenSexyBack", "女性性感-背部"),
    WomenSexyChest(TLabelEnum.Sexy,"WomenSexyChest", "女性性感-胸部"),
    WomenSexyLeg(TLabelEnum.Sexy,"WomenSexyLeg", "女性性感-腿部"),
    ACGSexy(TLabelEnum.Sexy,"ACGSexy", "ACG性感"),
    MiddleFinger(TLabelEnum.Sexy,"MiddleFinger", "竖中指"),
    Kiss(TLabelEnum.Sexy,"Kiss", "亲吻"),
    HipCloseUp(TLabelEnum.Sexy,"HipCloseUp", "臀部性感"),
    ChestCloseUp(TLabelEnum.Sexy,"ChestCloseUp", "胸部特写"),
    FootCloseUp(TLabelEnum.Sexy,"FootCloseUp", "足部特写"),
    CrotchCloseUp(TLabelEnum.Sexy,"CrotchCloseUp", "亲密行为"),
    SexySum(TLabelEnum.Sexy,"SexySum", "全局性感"),
    VulgarLeg(TLabelEnum.Sexy,"VulgarLeg", "低俗腿部"),
    VulgarChest(TLabelEnum.Sexy,"VulgarChest", "低俗胸部"),
    Burka(TLabelEnum.Terror,"Burka", "特殊服饰"),
    Uniform(TLabelEnum.Terror,"Uniform", "军警制服"),
    Gun(TLabelEnum.Terror,"Gun", "枪支等热武器"),
    BigWeapon(TLabelEnum.Terror,"BigWeapon", "军事武器"),
    Knife(TLabelEnum.Terror,"Knife", "刀等冷兵器"),
    Crowd(TLabelEnum.Terror,"Crowd", "人群聚集"),
    Blood(TLabelEnum.Terror,"Blood", "血腥画面"),
    Fire(TLabelEnum.Terror,"Fire", "火灾爆炸"),
    ACGTerror(TLabelEnum.Terror,"ACGTerror", "ACG暴恐"),
    Horror(TLabelEnum.Terror,"Horror", "惊悚"),
    Smoking(TLabelEnum.Illegal,"Smoking", "吸烟"),
    Drink(TLabelEnum.Illegal,"Drink", "喝酒"),
    CarLive(TLabelEnum.Illegal,"CarLive", "车内直播"),
    BedLive(TLabelEnum.Illegal,"BedLive", "床上直播"),
    Drug(TLabelEnum.Illegal,"Drug", "吸毒"),
    Gambling(TLabelEnum.Illegal,"Gambling", "赌博"),
    Fight(TLabelEnum.Illegal,"Fight", "打斗"),
    Tattoo(TLabelEnum.Illegal,"Tattoo", "纹身"),
    Contraband(TLabelEnum.Illegal,"Contraband", "违禁品"),
    QrCode(TLabelEnum.Ad,"QrCode", "广告二维码 "),
    AppLogo(TLabelEnum.Ad,"AppLogo", "互联网应用台标"),
    MovieLogo(TLabelEnum.Ad,"MovieLogo", "电影台标"),
    CCTVLogo(TLabelEnum.Ad,"CCTVLogo", "央视台标"),
    LocalTVLogo(TLabelEnum.Ad,"LocalTVLogo", "地方卫视台标"),
    ForeignVideoAppLogo(TLabelEnum.Ad,"ForeignVideoAppLogo", "海外视频应用台标"),
    OlympicsLogo(TLabelEnum.Ad,"OlympicsLogo", "奥运台标"),
    ForeignTVLogo(TLabelEnum.Ad,"ForeignTVLogo", "海外电视台标"),
    Phone(TLabelEnum.Ad,"Phone", "手机"),
    NationalOfficial(TLabelEnum.Polity,"NationalOfficial", "国部级领导人"),
    ProvincialOfficial(TLabelEnum.Polity,"ProvincialOfficial", "省级领导人"),
    CountryOfficial(TLabelEnum.Polity,"CountryOfficial", "市/县级领导人"),
    TWOfficial(TLabelEnum.Polity,"TWOfficial", "台湾地区领导人"),
    HKOfficial(TLabelEnum.Polity,"HKOfficial", "香港特区领导人"),
    MacaoOfficial(TLabelEnum.Polity,"MacaoOfficial", "澳门特区领导人"),
    Martyrs(TLabelEnum.Polity,"Martyrs", "革命烈士"),
    ChinesePresident(TLabelEnum.Polity,"ChinesePresident", "一号领导人"),
    MainlandIllegalOfficial(TLabelEnum.Polity,"MainlandIllegalOfficial", "大陆地区落马官员"),
    MainlandOfficialRelative(TLabelEnum.Polity,"MainlandOfficialRelative", "大陆地区领导人亲属"),
    AntiParty(TLabelEnum.Polity,"AntiParty", "反党人士"),
    Splitter(TLabelEnum.Polity,"Splitter", "分裂份子"),
    NaziCriminal(TLabelEnum.Polity,"NaziCriminal", "纳粹战犯"),
    WarCriminal(TLabelEnum.Polity,"WarCriminal", "侵华战犯"),
    HistoricalPerson(TLabelEnum.Polity,"HistoricalPerson", "历史人物"),
    Terrorist(TLabelEnum.Polity,"Terrorist", "恐怖分子头目"),
    Cult(TLabelEnum.Polity,"Cult", "邪教组织人物"),
    ForeignOfficial(TLabelEnum.Polity,"ForeignOfficial", "外国/地区政治人物"),
    BadStar(TLabelEnum.Polity,"BadStar", "劣迹明星"),
    Inferior(TLabelEnum.Polity,"Inferior", "劣迹网红"),
    PolityLogo(TLabelEnum.Polity,"PolityLogo", "政治实体-中性"),
    SensitivePolityLogo(TLabelEnum.Polity,"SensitivePolityLogo", "政治实体-负面"),
    TianAnMen(TLabelEnum.Polity,"TianAnMen", "天安门"),
    RMYXJiNianBei(TLabelEnum.Polity,"RMYXJiNianBei", "人民英雄纪念碑"),
    RenMinDaHuiTang(TLabelEnum.Polity,"RenMinDaHuiTang", "人民大会堂"),
    HuaBiao(TLabelEnum.Polity,"HuaBiao", "华表"),
    MinZhuNvShengXiang64(TLabelEnum.Polity,"MinZhuNvShengXiang64", "64民主女神像"),
    LadyLibertyHK(TLabelEnum.Polity,"LadyLibertyHK", "香港民主女神像"),
    JingGuoShengShe(TLabelEnum.Polity,"JingGuoShengShe", "靖国神社"),
    WeiLingTa(TLabelEnum.Polity,"WeiLingTa", "慰灵塔"),
    FengTianZhongLingTa(TLabelEnum.Polity,"FengTianZhongLingTa", "奉天忠灵塔"),
    GuoShangZhiZhu(TLabelEnum.Polity,"GuoShangZhiZhu", "国殇之柱"),
    MinZhuLieShiBei(TLabelEnum.Polity,"MinZhuLieShiBei", "民主烈士纪念碑"),
    JiNianBei(TLabelEnum.Polity,"64JiNianBei", "64纪念碑"),
    RenMinDaHuiTangInner(TLabelEnum.Polity,"RenMinDaHuiTangInner", "人民大会堂内部"),
    CCPVirus(TLabelEnum.Polity,"CCPVirus", "中共病毒"),
    JingGangShanA(TLabelEnum.Polity,"JingGangShanA", "井冈山A"),
    JingGangShanB(TLabelEnum.Polity,"JingGangShanB", "井冈山B"),
    SexyBehavior(TLabelEnum.Porn,"SexyBehavior", "性行为"),
    WomenPrivatePart(TLabelEnum.Porn,"WomenPrivatePart", "女性下体"),
    WomenChest(TLabelEnum.Porn,"WomenChest", "女性胸部"),
    MenPrivatePart(TLabelEnum.Porn,"MenPrivatePart", "男性下体"),
    ButtocksExposed(TLabelEnum.Porn,"ButtocksExposed", "臀部"),
    NakedChild(TLabelEnum.Porn,"NakedChild", "儿童裸露"),
    ACGPorn(TLabelEnum.Porn,"ACGPorn", "ACG色情"),
    PornSum(TLabelEnum.Porn,"PornSum", "全局色情"),
    TouchChest(TLabelEnum.Porn,"TouchChest", "摸胸"),
    TouchHip(TLabelEnum.Porn,"TouchHip", "摸臀"),
    TouchTriangle(TLabelEnum.Porn,"TouchTriangle", "摸下体"),
    SM(TLabelEnum.Porn,"SM", "SM"),
    Tongue(TLabelEnum.Porn,"Tongue", "吐舌挑逗"),
    NakedAnimal(TLabelEnum.Porn,"NakedAnimal", "动物裸露"),
    SexAids(TLabelEnum.Porn,"SexAids", "性用品"),
    ;

    private TLabelEnum labelEnum;
    private String subLabel;
    private String desc;

    TSubLabelEnum(TLabelEnum labelEnum, String subLabel, String desc) {
        this.labelEnum = labelEnum;
        this.subLabel = subLabel;
        this.desc = desc;
    }

    public TLabelEnum getLabelEnum() {
        return labelEnum;
    }

    public void setLabelEnum(TLabelEnum labelEnum) {
        this.labelEnum = labelEnum;
    }

    public String getSubLabel() {
        return subLabel;
    }

    public void setSubLabel(String subLabel) {
        this.subLabel = subLabel;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static TSubLabelEnum of(String subLabel) {
        for (TSubLabelEnum labelEnum : TSubLabelEnum.values()) {
            if (labelEnum.getSubLabel().equals(subLabel)) {
                return labelEnum;
            }
        }
        return null;
    }
}
