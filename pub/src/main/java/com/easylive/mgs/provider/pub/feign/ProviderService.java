package com.easylive.mgs.provider.pub.feign;

import com.easylive.mgs.provider.pub.enums.DetectionServiceEnum;
import com.easylive.mgs.provider.pub.enums.ImageModerationEnum;
import com.easylive.mgs.provider.pub.model.*;
import com.easylive.mgs.provider.pub.model.location.GpsLocation;
import com.easylive.mgs.provider.pub.model.location.IpLocation;
import feign.Headers;
import feign.Param;
import feign.QueryMap;
import feign.RequestLine;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2020-06-01
 */
@FeignClient(value = "provider")
@Component
public interface ProviderService {

    @GetMapping("/porn/image/scan/ali")
    GreenImageScanResult pornImageScanAli(@Param("imageUrl") @RequestParam("imageUrl") String imageUrl);

    @GetMapping("/porn/image/scan")
    @RequestLine("GET /porn/image/scan?imageUrl={imageUrl}")
    GreenImageScanResult pornImageScan(@Param("imageUrl") @RequestParam("imageUrl") String imageUrl);

    @GetMapping("/porn/image/scan/ad")
    @RequestLine("GET /porn/image/scan/ad?imageUrl={imageUrl}")
    GreenImageScanResult pornImageScanAd(@Param("imageUrl") @RequestParam("imageUrl") String imageUrl);

    @RequestLine("GET /location/ip?ip={ip}")
    @GetMapping("/location/ip")
    IpLocation getLocationByIp(@Param("ip") @RequestParam("ip") String ip);

    @RequestLine("GET /location/gps?longitude={longitude}&latitude={latitude}")
    @GetMapping("/location/gps")
    GpsLocation getLocationByGps(@Param("longitude") @RequestParam("longitude") Double longitude,@Param("latitude") @RequestParam("latitude") Double latitude);

    @RequestLine("POST /porn/text/scan")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/porn/text/scan")
    GreenImageScanResult pornTextScan(@Param("content") @RequestParam("content") String content);

    /**
     * ali文本检查怎强版本
     * @param content
     * @param detectionService
     * @return
     */
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/porn/text/scan/enhance")
    ScanDTO textEnhanceScan(
            @Param("content") @RequestParam("content") String content,
            @Param("detectionService") @RequestParam("detectionService") DetectionServiceEnum detectionService
            );

    /**
     * ali图片检查怎强版本
     * @param url
     * @param imageModeration
     * @param dataId
     * @return
     */
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/porn/image/scan/enhance")
    GreenImageScanResult imageEnhanceScan(
            @Param("url") @RequestParam("url") String url,
            @Param("imageModeration") @RequestParam("imageModeration") ImageModerationEnum imageModeration,
            @Param("dataId") @RequestParam(value = "dataId", required = false) String dataId
    );

    /**
     * 创建腾讯云音频监控任务
     * @param bizId 业务唯一ID, 作为取消业务凭证 (格式: live-DataId3EO5tYKk29Bf9R)
     * @param content 监控内容 (目前支持url流)
     * @return data 任务ID
     */
    @RequestLine("POST /tencent/porn/audio/scan")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/tencent/porn/audio/scan")
    PronScanDTO tencentPornAudioScan(@Param("bizId") @RequestParam("bizId") String bizId, @Param("content") @RequestParam("content") String content);

    /**
     * 取消腾讯云音频监控任务
     * @param taskId 创建业务返回data
     */
    @RequestLine("POST /tencent/porn/audio/cancel")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/tencent/porn/audio/cancel")
    void tencentPornAudioCancel(@Param("taskId") @RequestParam("taskId") String taskId);

    /**
     * 创建腾讯云视频监控任务
     * @param bizId 业务唯一ID, 作为取消业务凭证 (格式: live-DataId3EO5tYKk29Bf9R)
     * @param content 监控内容 (目前支持url流)
     * @return data 任务ID
     */
    @RequestLine("POST /tencent/porn/video/scan")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/tencent/porn/video/scan")
    PronScanDTO tencentPornVideoScan(@Param("bizId") @RequestParam("bizId") String bizId, @Param("content") @RequestParam("content") String content);

    /**
     * 取消腾讯云视频监控任务
     * @param taskId 创建业务返回data
     */
    @RequestLine("POST /tencent/porn/video/cancel")
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/tencent/porn/video/cancel")
    void tencentPornVideoCancel(@Param("taskId") @RequestParam("taskId") String taskId);


    /**
     * 一句话语音识别
     * @param tencentVoiceAO
     */
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/tencent/porn/voice/scan")
    TencentVoiceDTO tencentVoiceScan(@QueryMap @SpringQueryMap TencentVoiceAO tencentVoiceAO);


    /**
     * 阿里云身份证ID、姓名二要素识别
     * @param idNo
     * @param name
     * @return
     */
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/id/card/meta/verify")
    boolean IDMetaVerify(@Param("idNo") @RequestParam("idNo") String idNo, @Param("name") @RequestParam("name") String name);

    /**
     * 创建阿里云视频监控任务
     * @param bizId 业务唯一ID, 作为取消业务凭证 (格式: video-DataId3EO5tYKk29Bf9R)
     * @param content 监控内容 (目前支持url流)
     * @return data 任务ID
     */
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/ali/porn/video/scan")
    PronScanDTO aliPornVideoScan(@Param("bizId") @RequestParam("bizId") String bizId, @Param("content") @RequestParam("content") String content);

}
