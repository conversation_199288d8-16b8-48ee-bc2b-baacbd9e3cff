package com.easylive.mgs.provider.pub.feign;

import com.easylive.mgs.provider.pub.model.WeiXinUserPhoneDTO;
import com.easylive.mgs.provider.pub.model.WeixinJscode2SessionDTO;
import feign.Headers;
import feign.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "provider")
@Component
public interface WeixinAppService {

    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/weixin/applet/scheme")
    String generateAppletScheme(
            @Param("appId") @RequestParam("appId") String appId,
            @Param("path") @RequestParam(value = "path") String path,
            @Param("query") @RequestParam(value = "query") String query,
            @Param("env") @RequestParam(value = "env") String env,
            @Param("validTime") @RequestParam(value = "validTime") int validTime
    );

    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/weixin/applet/jscode2session")
    WeixinJscode2SessionDTO jscode2Session(@Param("appId") @RequestParam("appId") String appId,
                                                  @Param("code") @RequestParam("code") String code);

    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/weixin/applet/getPhoneNumber")
    WeiXinUserPhoneDTO getPhoneNumber(@Param("appId") @RequestParam("appId") String appId,
                                      @Param("code") @RequestParam("code") String code);

}
