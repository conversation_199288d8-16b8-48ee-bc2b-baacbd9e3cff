package com.easylive.mgs.provider.pub.feign;

import com.easylive.mgs.provider.pub.model.CDNDomainDTO;
import com.easylive.mgs.provider.pub.model.CDNDomainSchedDTO;
import feign.Headers;
import feign.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "provider")
@Component
public interface CDNService {
    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/cdn/domain/list")
    List<CDNDomainDTO> listAll();

    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/cdn/refresh")
    String refresh(@Param("domain") @RequestParam("url") String url,
                   @Param("type") @RequestParam("type") String type,
                   @Param("force") @RequestParam("force") boolean force);

    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/cdn/sign")
    String sign(@Param("url") @RequestParam(value = "url") String url,
                @Param("expiresInSeconds") @RequestParam(value = "expires") long expires);

    @Headers("Content-Type: application/x-www-form-urlencoded")
    @PostMapping("/cdn/sched/list")
    List<CDNDomainSchedDTO> listSched();

}
