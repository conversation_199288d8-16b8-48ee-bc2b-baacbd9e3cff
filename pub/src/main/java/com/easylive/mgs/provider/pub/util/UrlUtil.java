package com.easylive.mgs.provider.pub.util;

import org.apache.http.client.utils.URIBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;
import java.util.Set;
import java.util.HashSet;
import java.nio.ByteBuffer;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.CodingErrorAction;
import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 * @date 2024/7/3
 */
public class UrlUtil {

    private static final ThreadLocal<CharsetDecoder> DECODER_THREAD_LOCAL =
        ThreadLocal.withInitial(() -> {
            CharsetDecoder decoder = StandardCharsets.UTF_8.newDecoder();
            decoder.onMalformedInput(CodingErrorAction.REPORT);
            decoder.onUnmappableCharacter(CodingErrorAction.REPORT);
            return decoder;
        });

    public static String build(String scheme, String host, String path) {
        return scheme + "://" + host + path;
    }
    public static String buildWithUrlEncode(String scheme, String host, String path) {
        int queryIndex = path.indexOf("?");
        String basePath = (queryIndex >= 0) ? path.substring(0, queryIndex) : path;
        String existingQuery = (queryIndex >= 0) ? path.substring(queryIndex + 1) : null;

        URIBuilder builder = new URIBuilder()
                .setScheme(scheme)
                .setHost(host)
                .setPath(basePath);
        if( existingQuery != null)
            builder.setCustomQuery(existingQuery);
        return builder.toString();
    }

    /**
     * 对输入字符串进行URL编码
     * @param str 要编码的字符串
     * @return
     */
    public static String encode(String str) {
        try {
            return URLEncoder.encode(str, StandardCharsets.UTF_8).replace("+", "%20");
        } catch (Exception e) {
            return str;
        }
    }

    /**
     * 对输入URL路径进行URL编码，忽略路径分隔符
     * @param path 输入路径
     * @return 编码后的路径
     */
    public static String encodePath(String path) {
        return encode(path).replace("%2F", "/");
    }


    /**
     * 对URL进行安全编码（区分路径和参数部分）
     * @param url 完整URL（含协议、路径、参数等）
     * @return 符合RFC标准的编码后URL
     */
    public static String encodeURL(String url) {
        if (url == null || url.isEmpty()) {
            return url;
        }
        
        // 1. 分离协议、域名、路径、查询参数和fragment
        String[] urlParts = splitURL(url);
        String protocol = urlParts[0];
        String domain = urlParts[1];
        String path = encodePathSegments(urlParts[2]); // 路径分段编码
        String query = encodeQueryParams(urlParts[3]); // 参数键值对编码
        String fragment = encodeFragment(urlParts[4]); // fragment编码

        // 重组URL
        StringBuilder result = new StringBuilder();
        if (!protocol.isEmpty()) result.append(protocol).append("://");
        result.append(domain);
        if (!path.isEmpty()) result.append(path);
        if (!query.isEmpty()) result.append("?").append(query);
        if (!fragment.isEmpty()) result.append("#").append(fragment);
        return result.toString();
    }

    /**
     * 对单个URL组件做严格百分号编码
     * @param input
     * @return
     */
    public static String encodeURLComponent(String input) {
        return percentEncodeUtf8(input, UrlUtil::isUnreserved);
    }

    // URL结构拆分（协议/域名/路径/参数/fragment）
    public static String[] splitURL(String url) {
        String protocol = "";
        String domain = "";
        String path = "";
        String query = "";
        String fragment = "";

        // 分离fragment
        int fragmentIndex = url.indexOf("#");
        if (fragmentIndex != -1) {
            fragment = url.substring(fragmentIndex + 1);
            url = url.substring(0, fragmentIndex);
        }

        // 分离协议
        int protocolEnd = url.indexOf("://");
        if (protocolEnd != -1) {
            protocol = url.substring(0, protocolEnd);
            url = url.substring(protocolEnd + 3);
        }

        // 分离域名和路径
        int domainEnd = url.indexOf("/");
        if (domainEnd == -1) {
            // 没有路径，检查是否有查询参数
            int queryStart = url.indexOf("?");
            if (queryStart != -1) {
                domain = url.substring(0, queryStart);
                query = url.substring(queryStart + 1);
            } else {
                domain = url;
            }
        } else {
            domain = url.substring(0, domainEnd);
            url = url.substring(domainEnd);

            // 分离路径和查询参数
            int queryStart = url.indexOf("?");
            if (queryStart != -1) {
                path = url.substring(0, queryStart);
                query = url.substring(queryStart + 1);
            } else {
                path = url;
            }
        }
        return new String[]{protocol, domain, path, query, fragment};
    }


    // 核心1：路径分段编码（保留/分隔符）
    private static String encodePathSegments(String path) {
        if (path == null || path.isEmpty()) return "";

        // 以 / 开头的 path，分割后第一个 segment 是空字符串
        String[] segments = path.split("/", -1);
        StringBuilder encodedPath = new StringBuilder();

        for (int i = 0; i < segments.length; i++) {
            if (i > 0) {
                encodedPath.append("/");
            }
            String segment = segments[i];
            if (!segment.isEmpty()) {
                encodedPath.append(encodePathComponent(segment));
            }
            // 如果 segment 为空，直接加分隔符（已在上面 append("/"); 处理）
        }
        // 如果 path 以 / 结尾，split 会保留最后的空字符串，逻辑也能保留结尾斜杠
        return encodedPath.toString();
    }

    // 核心2：查询参数编码（区分键值）
    private static String encodeQueryParams(String query) {
        if (query == null || query.isEmpty()) return "";

        StringBuilder encodedQuery = new StringBuilder();
        String[] pairs = query.split("&");
        
        for (int i = 0; i < pairs.length; i++) {
            String pair = pairs[i];
            if (i > 0) encodedQuery.append("&");
            
            if (pair.contains("=")) {
                String[] kv = pair.split("=", 2);
                String key = encodeQueryComponent(kv[0]);
                String value = kv.length > 1 ? encodeQueryComponent(kv[1]) : "";
                encodedQuery.append(key).append("=").append(value);
            } else {
                // 处理无值参数（如?debug）
                encodedQuery.append(encodeQueryComponent(pair));
            }
        }
        return encodedQuery.toString();
    }

    // fragment编码
    private static String encodeFragment(String fragment) {
        if (fragment == null || fragment.isEmpty()) return "";
        return encodeFragmentComponent(fragment);
    }

    // 允许的特殊符号集合
    private static final Set<Integer> PATH_EXTRA = new HashSet<>();
    private static final Set<Integer> QUERY_EXTRA = new HashSet<>();
    static {
        for (char c : "!$&'()*+,;=:@".toCharArray()) PATH_EXTRA.add((int)c);
        QUERY_EXTRA.addAll(PATH_EXTRA);
        for (char c : "/?".toCharArray()) QUERY_EXTRA.add((int)c);
    }

    // unreserved 判断
    private static boolean isUnreserved(int cp) {
        return (cp >= 'A' && cp <= 'Z') || (cp >= 'a' && cp <= 'z') || (cp >= '0' && cp <= '9')
                || cp == '-' || cp == '_' || cp == '.' || cp == '~';
    }

    // 路径 allowed 判断
    private static boolean isPathAllowed(int cp) {
        return isUnreserved(cp) || PATH_EXTRA.contains(cp);
    }
    // 查询 allowed 判断
    private static boolean isQueryAllowed(int cp) {
        return isUnreserved(cp) || QUERY_EXTRA.contains(cp);
    }
    // fragment allowed 判断
    private static boolean isFragmentAllowed(int cp) {
        return isQueryAllowed(cp);
    }

    private static String percentEncodeUtf8(String input, java.util.function.IntPredicate isAllowed) {
        if (input == null) return "";
        StringBuilder sb = new StringBuilder(input.length() + 16);
        input.codePoints().forEach(cp -> {
            if (isAllowed.test(cp)) {
                sb.appendCodePoint(cp);
            } else {
                String s = new String(Character.toChars(cp));
                byte[] bytes = s.getBytes(StandardCharsets.UTF_8);
                for (byte b : bytes) {
                    sb.append(String.format("%%%02X", b & 0xFF));
                }
            }
        });
        return sb.toString();
    }


    private static String encodePathComponent(String input) {
        if (input == null) return "";
        if (isValidPathComponent(input)) return input;
        return percentEncodeUtf8(input, UrlUtil::isPathAllowed);
    }
    private static String encodeQueryComponent(String input) {
        if (input == null) return "";
        if (isValidQueryComponent(input)) return input;
        return percentEncodeUtf8(input, UrlUtil::isQueryAllowed);
    }
    private static String encodeFragmentComponent(String input) {
        if (input == null) return "";
        if (isValidFragmentComponent(input)) return input;
        return percentEncodeUtf8(input, UrlUtil::isFragmentAllowed);
    }

    // RFC 3986 unreserved字符检查
    private static boolean isUnreservedChar(char c) {
        return (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') || 
               (c >= '0' && c <= '9') || c == '-' || c == '.' || c == '_' || c == '~';
    }

    // RFC 3986 sub-delims字符检查
    private static boolean isSubDelimChar(char c) {
        return c == '!' || c == '$' || c == '&' || c == '\'' || c == '(' || c == ')' ||
               c == '*' || c == '+' || c == ',' || c == ';' || c == '=';
    }

    // 检查路径组件是否已正确编码
    static Pattern invalidPathPattern = Pattern.compile("[^A-Za-z0-9\\-._~!$&'()*+,;=:@%]");
    static Pattern percentPattern = Pattern.compile("%[0-9A-Fa-f]{2}");
    static Pattern invalidQueryPattern = Pattern.compile("[^A-Za-z0-9\\-._~!$&'()*+,;=:@/?%]");


    public static boolean isValidPathComponent(String input) {
        return isValid(input, UrlUtil::isPathAllowed, true);
    }

    public static boolean isValidQueryComponent(String input) {
        return isValid(input, UrlUtil::isQueryAllowed, false);
    }

    private static boolean isValid(String input, java.util.function.IntPredicate isAllowed, boolean isPath) {
        if (input == null) return false;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        for (int i = 0; i < input.length(); ) {
            char c = input.charAt(i);
            if (c == '%') {
                if (i + 2 >= input.length()) { return false; }
                try {
                    baos.write(Integer.parseInt(input.substring(i + 1, i + 3), 16));
                    i += 3;
                } catch (NumberFormatException e) {
                    return false;
                }
            } else {
                if (baos.size() > 0) {
                    try {
                        DECODER_THREAD_LOCAL.get().decode(ByteBuffer.wrap(baos.toByteArray()));
                    } catch (Exception e) {
                        return false;
                    }
                    baos.reset();
                }
                int cp = input.codePointAt(i);
                if (isPath && cp == '/') {
                    // ok
                } else if (!isAllowed.test(cp)) {
                    return false;
                }
                i += Character.charCount(cp);
            }
        }

        if (baos.size() > 0) {
            try {
                DECODER_THREAD_LOCAL.get().decode(ByteBuffer.wrap(baos.toByteArray()));
            } catch (Exception e) {
                return false;
            }
        }
        return true;
    }

    // 检查fragment组件是否已正确编码
    private static boolean isValidFragmentComponent(String input) {
        return isValidQueryComponent(input); // fragment和query规则相同
    }



}
