package com.easylive.mgs.provider.pub.enums;

/**
 * CDN签名类型
 * <AUTHOR>
 * @date 2024/6/27
 */
public enum CDNSignType {
    DEFAULT("DEFAULT"),
    A("A"), //
    B("B"), //
    C("C"), //
    D("D"); //

    private final String name;

    CDNSignType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static CDNSignType fromName(String name) {
        if( name == null )
            return null;

        name = name.toUpperCase();

        for (CDNSignType type : CDNSignType.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
}
