package com.easylive.mgs.provider.pub.stub;

import lombok.extern.slf4j.Slf4j;

/**
 * 本地缓存自动重加载的模板抽象类，子类继承后实现internalReload加载资源到本地，并且在使用本地资源前调用reloadCache(false)
 *
 * <AUTHOR>
 * @date 2024/7/3
 */
@Slf4j
public abstract class ReloadableCache {
    private static final long DEFAULT_CACHE_TIME_IN_SECONDS = 60 * 5;
    private static final long ERROR_RETRY_INTERVAL = 10;
    private long lastTime = 0L;
    private long cacheTimeInSeconds = DEFAULT_CACHE_TIME_IN_SECONDS;
    private long currentReloadInterval = DEFAULT_CACHE_TIME_IN_SECONDS;
    private static final long MIN_CACHE_TIME_IN_SECONDS = 10;
    private boolean autoReload = true;


    public void setCacheTime(long cacheTime) {
        this.cacheTimeInSeconds = Math.max(cacheTime, MIN_CACHE_TIME_IN_SECONDS);
    }

    public void setAutoReload(boolean autoReload) {
        this.autoReload = autoReload;
    }

    public void load() {
        reloadCache(true);
    }

    synchronized protected void  reloadCache(boolean force) {
        long current = System.currentTimeMillis() / 1000;
        if (!force && current - lastTime < currentReloadInterval) {
            return;
        }

        if (!force && !autoReload) {
            return;
        }

        try {
            internalReload();
        } catch (Exception e) {
            log.error("Reload cache error", e);
            lastTime = System.currentTimeMillis();
            currentReloadInterval = ERROR_RETRY_INTERVAL;
            return;
        }

        lastTime = System.currentTimeMillis() / 1000;
        currentReloadInterval = cacheTimeInSeconds;
    }

    protected abstract void internalReload();
}
