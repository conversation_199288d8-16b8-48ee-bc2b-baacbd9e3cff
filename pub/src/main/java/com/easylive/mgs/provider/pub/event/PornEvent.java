package com.easylive.mgs.provider.pub.event;

import com.easylive.mgs.provider.pub.enums.PornTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PornEvent {
    /**
     * 业务类型
     */
    private PornTypeEnum type;

    /**
     * 业务Id
     */
    private String bizId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务建议
     */
    private String suggestion;

    /**
     * 任务标签
     */
    private String label;

    /**
     * 二级任务标签
     */
    private String subLabel;

    /**
     * 任务评分
     */
    private Integer score;

    /**
     * 任务内容, 音频切片、视频图片 - 保存有效1天
     */
    private String content;

    public PornEvent(PornTypeEnum type, String bizId, String taskId, String suggestion, String label, String subLabel, Integer score, String content) {
        this.bizId = bizId;
        this.taskId = taskId;
        this.suggestion = suggestion;
        this.label = label;
        this.score = score;
        this.type = type;
        this.content = content;
        this.subLabel = subLabel;
    }

    public PornEvent() {
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getSuggestion() {
        return suggestion;
    }

    public void setSuggestion(String suggestion) {
        this.suggestion = suggestion;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public PornTypeEnum getType() {
        return type;
    }

    public void setType(PornTypeEnum type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSubLabel() {
        return subLabel;
    }

    public void setSubLabel(String subLabel) {
        this.subLabel = subLabel;
    }

    @Override
    public String toString() {
        return "PornEvent{" +
                "type=" + type +
                ", bizId='" + bizId + '\'' +
                ", taskId='" + taskId + '\'' +
                ", suggestion='" + suggestion + '\'' +
                ", label='" + label + '\'' +
                ", subLabel='" + subLabel + '\'' +
                ", score=" + score +
                ", content='" + content + '\'' +
                '}';
    }
}
