package com.easylive.mgs.provider.pub.cdn;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
public class BaiduURLSignerFactory {
    public static URLSigner typeC() {
        return (signKey, path, expireTime) -> {
            String hexString = Long.toHexString(expireTime);
            String orgString = signKey + path + hexString;
            String md5Hash = DigestUtils.md5Hex(orgString);
            return path+"?md5hash="+md5Hash+"&timestamp="+hexString;
        };
    }
}

