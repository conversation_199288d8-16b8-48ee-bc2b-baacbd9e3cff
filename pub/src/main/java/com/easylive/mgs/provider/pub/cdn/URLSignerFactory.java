package com.easylive.mgs.provider.pub.cdn;

import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.enums.CDNDomainType;
import com.easylive.mgs.provider.pub.enums.CDNSignType;
import com.easylive.mgs.provider.pub.enums.CloudProviderType;
import com.easylive.mgs.provider.pub.util.UrlUtil;
import com.easylive.rpc.http.ResponseException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/28
 */
@Slf4j
public class URLSignerFactory {
    private static final Map<String, URLSigner> signers = new HashMap<>();
    private static final URLSigner defaultSigner = (signKey, path, expireTime) -> "";

    static {
        // CDN
        signers.put(getKey(CloudProviderType.ALI, CDNSignType.C), AliURLSignerFactory.typeC());
        signers.put(getKey(CloudProviderType.ALI, CDNSignType.B), AliURLSignerFactory.typeB());
        signers.put(getKey(CloudProviderType.ALI, CDNSignType.DEFAULT, CDNDomainType.PUSH), AliURLSignerFactory.typeLive());
        signers.put(getKey(CloudProviderType.ALI, CDNSignType.DEFAULT, CDNDomainType.PULL), AliURLSignerFactory.typeLive());
        signers.put(getKey(CloudProviderType.TENCENT, CDNSignType.D), TencentURLSignerFactory.typeD());
        signers.put(getKey(CloudProviderType.BAIDU, CDNSignType.C), BaiduURLSignerFactory.typeC());
        signers.put(getKey(CloudProviderType.HUOSHAN, CDNSignType.C), HuoshanURLSignerFactory.typeC());
        signers.put(getKey(CloudProviderType.HUOSHAN, CDNSignType.B), HuoshanURLSignerFactory.typeB());
        signers.put(getKey(CloudProviderType.QINIU, CDNSignType.DEFAULT), QiniuURLSignerFactory.typeDefault());

        // 直播
        signers.put(getKey(CloudProviderType.TENCENT, CDNSignType.DEFAULT, CDNDomainType.PUSH), TencentURLSignerFactory.typeLive());
        signers.put(getKey(CloudProviderType.TENCENT, CDNSignType.DEFAULT, CDNDomainType.PULL), TencentURLSignerFactory.typeLive());
        signers.put(getKey(CloudProviderType.HUOSHAN, CDNSignType.DEFAULT, CDNDomainType.PULL), HuoshanURLSignerFactory.typeLive());
        signers.put(getKey(CloudProviderType.HUOSHAN, CDNSignType.DEFAULT, CDNDomainType.PUSH), HuoshanURLSignerFactory.typeLive());

    }

    private static String getKey(CloudProviderType providerType, CDNSignType signType) {
        return getKey(providerType, signType, CDNDomainType.CDN);
    }

    private static String getKey(CloudProviderType providerType, CDNSignType signType, CDNDomainType type) {
        if (signType == null)
            signType = CDNSignType.DEFAULT;
        if (type == null)
            type = CDNDomainType.CDN;

        return providerType.name() + "_" + signType.name() + "_" + type.name();
    }

    public static URLSigner getSigner(CloudProviderType providerType, CDNSignType signType, CDNDomainType type) {
        return signers.get(getKey(providerType, signType, type));
    }

    public static URLSigner getCDNSigner(CloudProviderType providerType, CDNSignType signType) {
        return getSigner(providerType, signType, CDNDomainType.CDN);
    }

    public static URLSigner getSigner(String providerType, String signType) {
        return getSigner(CloudProviderType.fromName(providerType), CDNSignType.fromName(signType), CDNDomainType.CDN);
    }

    public static URLSigner getSignerWithDefault(CloudProviderType providerType, CDNSignType signType) {
        return signers.getOrDefault(getKey(providerType, signType), defaultSigner);
    }

    public static String sign(DomainSignResolver resolver, String url, long expiresInSeconds) {
        try {
            String scheme, host, path, query;

            // 检查是否是RTMP协议
            if (url.toLowerCase().startsWith("rtmp://")) {
                // 手动解析RTMP URL
                String noScheme = url.substring(7); // 移除"rtmp://"
                int hostEndIndex = noScheme.indexOf('/');

                if (hostEndIndex == -1) {
                    // 没有路径部分
                    host = noScheme;
                    path = "";
                } else {
                    host = noScheme.substring(0, hostEndIndex);
                    path = noScheme.substring(hostEndIndex);
                }

                // 处理查询参数
                int queryIndex = path.indexOf('?');
                if (queryIndex != -1) {
                    query = path.substring(queryIndex + 1);
                    path = path.substring(0, queryIndex);
                } else {
                    query = null;
                }

                scheme = "rtmp";
            } else {
                // 对于其他协议，使用标准URL类
                URL l = new URL(url);
                scheme = l.getProtocol();
                host = l.getHost();
                path = l.getPath();
                query = l.getQuery();
            }

            // 处理查询参数
            if (!StringUtils.isEmpty(query)) {
                path += "?" + query;
            }

            return sign(resolver, scheme, host, path, expiresInSeconds);
        } catch (MalformedURLException e) {
            log.error("Invalid url: {}", url);
            throw new ResponseException(ProviderError.E_CDN_SIGN, "Invalid url");
        }
    }

    private static String sign(DomainSignResolver resolver, String scheme, String domain, String path, long expiresInSeconds) {
        //TODO: path里面如果出现特殊字符的，需要urlencode，但是又不能对/?进行编码
        path = UrlUtil.encodePath(path);

        if (expiresInSeconds < 0) {
            throw new ResponseException(ProviderError.E_CDN_SIGN, "Invalid expire time");
        }
        DomainSignInfo info = resolver.resolve(domain);
        if (info == null) {
            log.error("CDN domain not found, scheme: {}, domain: {}, path: {}, expiresInSeconds: {}", scheme, domain, path, expiresInSeconds);
            throw new ResponseException(ProviderError.E_CDN_SIGN, "domain not found");
        }

        URLSigner signer = URLSignerFactory.getSigner(info.getProviderType(), info.getSignType(), info.getDomainType());
        if (signer == null) {
            log.error("signer not supported, domain: {}, type:{}, path: {}, providerType: {}, signType: {},", domain, info.getDomainType(), path, info.getProviderType(), info.getSignType());
            throw new ResponseException(ProviderError.E_CDN_SIGN, "sign not supported");
        }

        long timestamp = System.currentTimeMillis() / 1000 + expiresInSeconds;
        String sign = signer.sign(info.getSignKey(), path, timestamp);
        return UrlUtil.build(scheme, domain, sign);
    }


}
