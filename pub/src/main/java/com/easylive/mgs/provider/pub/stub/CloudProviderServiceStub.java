package com.easylive.mgs.provider.pub.stub;

import com.easylive.mgs.provider.pub.feign.CloudProviderService;
import com.easylive.mgs.provider.pub.model.CloudProviderAccessDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 拉取云厂商信息，存放到本地，需要定期重新拉取
 *
 * <AUTHOR>
 * @date 2024/07/02
 */

@Slf4j
@Component
public class CloudProviderServiceStub extends ReloadableCache {


    private final CloudProviderService cloudProviderService;

    private volatile Map<String, CloudProviderAccessDTO> cloudProviderMap = new HashMap<>();
    private volatile Map<Integer, CloudProviderAccessDTO> cloudProviderIdMap = new HashMap<>();


    public CloudProviderServiceStub(CloudProviderService cloudProviderService) {
        this.cloudProviderService = cloudProviderService;
    }

    @Override
    protected void internalReload() {
        Map<String, CloudProviderAccessDTO> tmpMap = new HashMap<>();
        Map<Integer, CloudProviderAccessDTO> tmpIdMap = new HashMap<>();

        cloudProviderService.getAllAccess().forEach(cloudProviderAccessDTO -> {
            tmpMap.put(cloudProviderAccessDTO.getName(), cloudProviderAccessDTO);
            tmpIdMap.put(cloudProviderAccessDTO.getId(), cloudProviderAccessDTO);
        });

        this.cloudProviderMap = tmpMap;
        this.cloudProviderIdMap = tmpIdMap;
        log.info("{} access loaded", tmpMap.size());
    }

    public List<CloudProviderAccessDTO> listAll() {
        reloadCache(false);
        return new ArrayList<>(cloudProviderMap.values());
    }

    public CloudProviderAccessDTO getAccessByName(String name) {
        reloadCache(false);
        return cloudProviderMap.get(name);
    }

    public CloudProviderAccessDTO getAccessById(Integer id) {
        reloadCache(false);
        return cloudProviderIdMap.get(id);
    }
}
