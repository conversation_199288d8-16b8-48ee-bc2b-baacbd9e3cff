package com.easylive.mgs.provider.pub.model;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/03/21
 */
public class CDNDomainSchedDTO {
    private String name;
    private List<TimeRange> validTime;
    private String description;
    private List<CDNDomainSchedItemDTO> items = new ArrayList<>();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<TimeRange> getValidTime() {
        return validTime;
    }

    public void setValidTime(List<TimeRange> validTime) {
        this.validTime = validTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<CDNDomainSchedItemDTO> getItems() {
        return items;
    }

    public void setItems(List<CDNDomainSchedItemDTO> items) {
        this.items = items;
    }

    public static class TimeRange {
        private LocalTime start;
        private LocalTime end;
        public TimeRange() {
        }

        public TimeRange(LocalTime start, LocalTime end) {
            this.start = start;
            this.end = end;
        }

        public void setStart(LocalTime start) {
            this.start = start;
        }

        public void setEnd(LocalTime end) {
            this.end = end;
        }

        public LocalTime getStart() {
            return start;
        }

        public LocalTime getEnd() {
            return end;
        }
    }

    public static class CDNDomainSchedItemDTO {
        private String domain;
        private int weight;

        public String getDomain() {
            return domain;
        }

        public void setDomain(String domain) {
            this.domain = domain;
        }

        public int getWeight() {
            return weight;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }
    }
}
