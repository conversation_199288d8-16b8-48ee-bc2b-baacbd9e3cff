package com.easylive.mgs.provider.pub.enums;

/**
 * CDN刷新类型
 * <AUTHOR>
 * @date 2024/6/27
 */
public enum CDNRefreshType {
    FILE("file"), // 文件刷新
    DIRECTORY("directory"), // 目录刷新
    REGEX("regex"), // 正则刷新
    PRELOAD("preload"), // 预热刷新
    IGNORE_PARAMS("ignoreParams"); // 忽略参数刷新


    private final String name;

    CDNRefreshType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static CDNRefreshType fromName(String name) {
        for (CDNRefreshType type : CDNRefreshType.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
}
