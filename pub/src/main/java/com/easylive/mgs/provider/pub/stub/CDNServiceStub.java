package com.easylive.mgs.provider.pub.stub;

import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.cdn.DomainSignInfo;
import com.easylive.mgs.provider.pub.cdn.URLSignerFactory;
import com.easylive.mgs.provider.pub.enums.CDNDomainType;
import com.easylive.mgs.provider.pub.enums.CDNSignType;
import com.easylive.mgs.provider.pub.enums.CloudProviderType;
import com.easylive.mgs.provider.pub.feign.CDNService;
import com.easylive.mgs.provider.pub.model.CDNDomainDTO;
import com.easylive.mgs.provider.pub.model.CDNDomainSchedDTO;
import com.easylive.rpc.http.ResponseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CDN提供服务
 *
 * <AUTHOR>
 * @date 2024/07/02
 */


@Slf4j
@Component
public class CDNServiceStub extends ReloadableCache {
    private static final long DEFAULT_SIGN_EXPIRED_TIME_IN_SECONDS = 3600;

    private final CDNService cdnService;
    private volatile Map<String, CDNDomainDTO> domainMap = new HashMap<>();
    private volatile Map<String, CDNDomainSchedDTO> schedMap = new HashMap<>();


    public CDNServiceStub(CDNService cdnService) {
        this.cdnService = cdnService;
    }



    @Override
    protected void internalReload() {
        Map<String, CDNDomainDTO> tmpMap = new HashMap<>();
        cdnService.listAll().forEach(cdnDomainDTO -> tmpMap.put(cdnDomainDTO.getName(), cdnDomainDTO));
        this.domainMap = tmpMap;
        log.info("{} cdn domain loaded", tmpMap.size());

        Map<String, CDNDomainSchedDTO> tmpSchedMap = new HashMap<>();
        cdnService.listSched().forEach(cdnDomainSchedDTO -> tmpSchedMap.put(cdnDomainSchedDTO.getName(), cdnDomainSchedDTO));
        this.schedMap = tmpSchedMap;
        log.info("{} cdn sched loaded", tmpSchedMap.size());
    }

    private CDNDomainDTO getDomain(String domain) {
        reloadCache(false);
        return domainMap.get(domain);
    }

    /**
     * 给一个指定CDN URL进行签名，使用默认的签名有效时长(3600秒)， 如果无法签名或者出错，则返回null
     *
     * @param url 需要签名的URL，域名必须是在Provider中进行管理的
     * @return 签名后的URL
     */
    public String sign(String url) {
        return sign(url, DEFAULT_SIGN_EXPIRED_TIME_IN_SECONDS);
    }

    /**
     * 给一个指定CDN URL进行签名， 如果无法签名或者出错，则返回null
     *
     * @param url              需要签名的URL，域名必须是在Provider中进行管理的
     * @param expiresInSeconds 签名额外有效时长。总有效时长为域名配置的时长加此时长
     * @return 签名后的URL
     */
    public String sign(String url, long expiresInSeconds) {
        String ret = "";
        try {
            ret = URLSignerFactory.sign(domain -> {
                CDNDomainDTO domainDTO = getDomain(domain);
                if (domainDTO == null)
                    throw new ResponseException(ProviderError.E_CDN_SIGN, "domain not exists");
                DomainSignInfo info = new DomainSignInfo();
                info.setProviderType(CloudProviderType.fromName(domainDTO.getProviderType()));
                info.setSignKey(domainDTO.getSignKey());
                info.setSignType(CDNSignType.fromName(domainDTO.getSignType()));
                info.setDomainType(CDNDomainType.fromValue(domainDTO.getType()));
                return info;
            }, url, expiresInSeconds);
        } catch (Exception e) {
            log.error("Error sign url:{}", url, e);
        }
        return ret;
    }

    public CDNDomainSchedDTO getSched(String name) {
        reloadCache(false);
        return schedMap.get(name);
    }

    /**
     * 对输入的URL进行调度，按照指定的调度名称的顺序优先级进行调度，如果无法调度，则返回null
     * @param url 需要调度的URL
     * @param schedNameList 调度的名称列表，优先匹配前面的
     * @return 调度后的URL，不带签名，如果无法调度，则返回输入url
     */
    public String sched(String url, List<String> schedNameList) {
        if (url == null)
            return null;

        if (schedNameList == null || schedNameList.isEmpty())
            return url;
        // 将url去掉query
        url = url.split("\\?")[0];

        LocalTime now = LocalTime.now();
        String domain = url.split("/")[2];
        for (String schedName : schedNameList) {
            CDNDomainSchedDTO sched = getSched(schedName);
            if (sched == null)
                continue;

            // 检查时间范围
            boolean isInValidTime = false;
            for (CDNDomainSchedDTO.TimeRange range : sched.getValidTime()) {
                if (range.getStart().isBefore(now) && range.getEnd().isAfter(now)) {
                    isInValidTime = true;
                    break;
                }
            }

            if (!isInValidTime)
                continue;

            int totalWeight = 0;
            for (CDNDomainSchedDTO.CDNDomainSchedItemDTO item : sched.getItems()) {
                totalWeight += item.getWeight();
            }

            //根据Weight 随机选择一个域名
            int random = (int) (Math.random() * totalWeight);
            for (CDNDomainSchedDTO.CDNDomainSchedItemDTO item : sched.getItems()) {
                random -= item.getWeight();
                if (random < 0) {
                    //将URL中的域名替换，并且签名
                    String newUrl = url.replace(domain, item.getDomain());
                    return sign(newUrl);
                }
            }

        }

        return url;
    }
}
