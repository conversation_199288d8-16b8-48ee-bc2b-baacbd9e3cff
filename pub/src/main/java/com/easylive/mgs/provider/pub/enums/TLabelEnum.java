package com.easylive.mgs.provider.pub.enums;

/**
 * 图片检测返回模型
 *
 * <AUTHOR>
 * @date 2020/6/1
 */
public enum TLabelEnum {
    Sexy("Sexy", "性感"),

    Terror("<PERSON>", "暴恐"),

    <PERSON>legal("<PERSON>leg<PERSON>", "违法"),

    Ad("Ad", "广告"),

    <PERSON><PERSON>("Polity", "涉政"),

    <PERSON>rn("Porn", "色情"),

    <PERSON><PERSON>("Abuse", "谩骂")
    ;

    private String label;
    private String desc;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    TLabelEnum(String label, String desc) {
        this.label = label;
        this.desc = desc;
    }
}
