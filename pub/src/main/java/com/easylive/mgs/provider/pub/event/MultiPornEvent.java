package com.easylive.mgs.provider.pub.event;

import com.easylive.mgs.provider.pub.enums.PornTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MultiPornEvent {
    /**
     * 业务类型
     */
    private PornTypeEnum type;

    /**
     * 业务Id
     */
    private String bizId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务结果集合
     */
    private List<Data> list;

    public static class Data {
        /**
         * 业务类型
         */
        private PornTypeEnum subType;

        /**
         * 任务建议
         */
        private String suggestion;

        /**
         * 任务标签
         */
        private String label;

        /**
         * 二级任务标签
         */
        private String subLabel;

        /**
         * 任务评分
         */
        private Integer score;

        /**
         * 任务内容, 音频切片、视频图片 - 保存有效1天
         */
        private String content;

        public Data() {
        }

        public Data(PornTypeEnum subType, String suggestion, String label, String subLabel, Integer score, String content) {
            this.subType = subType;
            this.suggestion = suggestion;
            this.label = label;
            this.subLabel = subLabel;
            this.score = score;
            this.content = content;
        }

        public PornTypeEnum getSubType() {
            return subType;
        }

        public void setSubType(PornTypeEnum subType) {
            this.subType = subType;
        }

        public String getSuggestion() {
            return suggestion;
        }

        public void setSuggestion(String suggestion) {
            this.suggestion = suggestion;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getSubLabel() {
            return subLabel;
        }

        public void setSubLabel(String subLabel) {
            this.subLabel = subLabel;
        }

        public Integer getScore() {
            return score;
        }

        public void setScore(Integer score) {
            this.score = score;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        @Override
        public String toString() {
            return "Data{" +
                    "subType=" + subType +
                    ", suggestion='" + suggestion + '\'' +
                    ", label='" + label + '\'' +
                    ", subLabel='" + subLabel + '\'' +
                    ", score=" + score +
                    ", content='" + content + '\'' +
                    '}';
        }
    }

    public MultiPornEvent(PornTypeEnum type, String bizId, String taskId, List<Data> list) {
        this.type = type;
        this.bizId = bizId;
        this.taskId = taskId;
        this.list = list;
    }

    public MultiPornEvent() {
    }

    public PornTypeEnum getType() {
        return type;
    }

    public void setType(PornTypeEnum type) {
        this.type = type;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public List<Data> getList() {
        return list;
    }

    public void setList(List<Data> list) {
        this.list = list;
    }

    @Override
    public String toString() {
        return "MultiPornEvent{" +
                "type=" + type +
                ", bizId='" + bizId + '\'' +
                ", taskId='" + taskId + '\'' +
                ", list=" + list +
                '}';
    }
}
