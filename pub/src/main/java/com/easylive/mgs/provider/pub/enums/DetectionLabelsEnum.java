package com.easylive.mgs.provider.pub.enums;

/**
 * label
 */
public enum DetectionLabelsEnum {

    /**
     * 广告引流
     */
    AD("ad"),

    /**
     * 涉政内容
     */
    POLITICAL("political_content"),

    /**
     * 辱骂内容
     */
    PROFANITY("profanity"),

    /**
     * 违禁内容
     */
    CONTRABAND("contraband"),

    /**
     * 色情内容
     */
    SEXUAL("sexual_content"),

    /**
     * 暴恐内容
     */
    VIOLENCE("violence"),

    /**
     * 无意义内容
     */
    NONSENSE("nonsense"),

    /**
     * 不良内容
     */
    NEGATIVE("negative_content"),

    /**
     * 宗教内容
     */
    RELIGION("religion"),

    /**
     * 网络暴力
     */
    CYBERBULLYING("cyberbullying"),

    /**
     * 广告法合规
     */
    AD_COMPLIANCE("ad_compliance"),

    /**
     * 用户库命中
     */
    CUSTOMIZED("C_customized"),
    ;

    private final String label;


    DetectionLabelsEnum(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public static DetectionLabelsEnum of(String label) {
        for (DetectionLabelsEnum labelsEnum : DetectionLabelsEnum.values()) {
            if (labelsEnum.getLabel().equals(label)) {
                return labelsEnum;
            }
        }

        return null;
    }
}
