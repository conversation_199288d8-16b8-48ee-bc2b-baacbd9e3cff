package com.easylive.mgs.provider.pub.enums;

public enum DetectionServiceEnum {
    /**
     * 广告法合规检测
     */
    AD_COMPLIANCE("ad_compliance_detection"),


    /**
     * AIGC类文字检测
     */
    AI_ART("ai_art_detection"),

    /**
     * 私聊互动内容检测
     */
    CHAT("chat_detection"),

    /**
     * 公聊评论内容检测
     */
    COMMENT("comment_detection"),

    /**
     * 国际业务多语言检测
     */
    COMMENT_MULTILINGUAL_PRO("comment_multilingual_pro"),

    /**
     * 用户昵称检测
     */
    NICKNAME("nickname_detection"),

    /**
     * PGC教学物料检测
     */
    PGC("pgc_detection"),
    ;


    private String service;

    DetectionServiceEnum(String service) {
        this.service = service;
    }

    public String getService() {
        return service;
    }
}
