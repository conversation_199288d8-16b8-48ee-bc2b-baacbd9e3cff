package com.easylive.mgs.provider;

import com.alibaba.cloud.nacos.registry.NacosServiceRegistry;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.client.serviceregistry.Registration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/18
 */
@Component
@Endpoint(id = "prestop")
@ConditionalOnProperty(prefix = "spring.cloud.nacos.discovery", name = "server-addr")
@ConditionalOnClass({NacosServiceRegistry.class, Registration.class})
public class PreStopEndpoint {

    NacosServiceRegistry nacosServiceRegistry;

    Registration registration;

    public PreStopEndpoint(NacosServiceRegistry nacosServiceRegistry, Registration registration) {
        this.nacosServiceRegistry = nacosServiceRegistry;
        this.registration = registration;
    }

    @ReadOperation
    public String preStop() {
        nacosServiceRegistry.deregister(registration);
        return "ok";
    }
}
