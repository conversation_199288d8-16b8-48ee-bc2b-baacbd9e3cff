package com.easylive.mgs.provider.config;

import com.easylive.common.event.EventConsumer;
import com.easylive.common.event.EventProducer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2019-11-06
 */

@Configuration
public class KafkaConfig {

    @Value("${kafka.server.bootstrap}")
    private String bootStrapServer;

    @Value("${kafka.consumer.group-id}")
    protected String groupId;

    @Bean
    public Map<String, Object> configProps() {
        Map<String, Object> configProps = new HashMap<>(6);
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootStrapServer);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, "true");
        configProps.put(ProducerConfig.ACKS_CONFIG, "all");
        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, "15000");
        return configProps;
    }

    @Autowired
    @Qualifier("kafkaThreadPool")
    private Executor taskExecutor;

    @Bean
    public EventProducer eventProducer() {
        EventProducer eventProducer = new EventProducer(configProps());
        eventProducer.start(taskExecutor);
        return eventProducer;
    }

    @Bean
    public EventConsumer eventConsumer() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootStrapServer);
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        return new EventConsumer(configProps);
    }

    @Bean
    DisposableBean eventStopBean() {
        return new DisposableBean() {
            @Autowired
            private EventConsumer eventConsumer;

            @Autowired
            private EventProducer eventProducer;

            @Override
            public void destroy() throws Exception {
                eventProducer.stop();
                eventConsumer.stop();
            }
        };
    }


}
