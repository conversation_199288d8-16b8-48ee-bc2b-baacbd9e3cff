package com.easylive.mgs.provider.config;

import com.easylive.mgs.provider.model.GreenImageScanResult;
import com.easylive.mgs.provider.pub.model.ScanDTO;
import org.ehcache.Cache;
import org.ehcache.CacheManager;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.config.builders.ExpiryPolicyBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.core.spi.service.StatisticsService;
import org.ehcache.core.statistics.CacheStatistics;
import org.ehcache.core.statistics.DefaultStatisticsService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

import static java.time.temporal.ChronoUnit.HOURS;
import static java.time.temporal.ChronoUnit.SECONDS;

/**
 * <AUTHOR>
 * @date 2019/12/25
 */
@Configuration
public class EhCacheConfig {

    @Bean(name = "EhCacheManager")
    CacheManager getCacheManager() {
        CacheManager cacheManager = CacheManagerBuilder.newCacheManagerBuilder()
                .using(statisticsService())
                .build();
        cacheManager.init();
        return cacheManager;
    }

    @Bean(name = "TextCache")
    Cache<String, GreenImageScanResult> textScanCache() {
        return getCacheManager().createCache("TextCache",
                CacheConfigurationBuilder.newCacheConfigurationBuilder(String.class, GreenImageScanResult.class,
                        ResourcePoolsBuilder.heap(10000))
                        .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.of(600, SECONDS)))
                        .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.of(1, HOURS)))
                        .build());
    }

    @Bean(name = "TextEnhanceCache")
    Cache<String, ScanDTO> textEnhanceScanCache() {
        return getCacheManager().createCache("TextEnhanceCache",
                CacheConfigurationBuilder.newCacheConfigurationBuilder(String.class, ScanDTO.class,
                                ResourcePoolsBuilder.heap(25000))
                        .withExpiry(ExpiryPolicyBuilder.timeToIdleExpiration(Duration.of(12, HOURS)))
                        .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.of(24, HOURS)))
                        .build());
    }


    @Bean
    StatisticsService statisticsService() {
        return new DefaultStatisticsService();
    }

    @Bean("TextCacheStat")
    CacheStatistics textCacheStatistics() {
        textScanCache();
        return statisticsService().getCacheStatistics("TextCache");
    }

}
