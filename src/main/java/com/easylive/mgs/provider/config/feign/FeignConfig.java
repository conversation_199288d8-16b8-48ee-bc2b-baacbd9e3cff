package com.easylive.mgs.provider.config.feign;

import com.easylive.rpc.http.feign.ResponseErrorDecoder;
import feign.Logger;
import feign.codec.ErrorDecoder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignConfig {

    @Value("${provider.feign.logging.level:NONE}")
    private String loggingLevel;

    @Bean
    Logger.Level getLoggerLevel() {
        return Logger.Level.valueOf(loggingLevel.toUpperCase());
    }

    @Bean
    ErrorDecoder errorDecoder() {
        return new ResponseErrorDecoder();
    }

}
