package com.easylive.mgs.provider.config.ali;

import com.aliyun.cloudauth20190307.Client;
import com.aliyun.teaopenapi.models.Config;
import com.easylive.mgs.provider.config.OssConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AliVerifyConfig {

    @Autowired
    private OssConfig ossConfig;

    @Bean
    public Client ID2MetaVerify() throws Exception {
        Config config = new Config();
        config.setAccessKeyId(ossConfig.getPornAccessKeyId());
        config.setAccessKeySecret(ossConfig.getPornAccessKeySecret());
        config.setRegionId(ossConfig.getPornRegion());
        config.setEndpoint(ossConfig.getAuthEndpoint());
        config.setReadTimeout(6000);
        config.setConnectTimeout(3000);

        return new Client(config);
    }
}
