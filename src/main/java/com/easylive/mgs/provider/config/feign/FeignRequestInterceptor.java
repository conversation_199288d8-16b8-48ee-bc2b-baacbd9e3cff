package com.easylive.mgs.provider.config.feign;

import com.easylive.mgs.provider.common.Constants;
import com.easylive.rpc.http.feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignRequestInterceptor extends RequestInterceptor {

    public FeignRequestInterceptor() {
        super(Constants.DEFAULT_USER_AGENT);
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        super.apply(requestTemplate);
    }
}
