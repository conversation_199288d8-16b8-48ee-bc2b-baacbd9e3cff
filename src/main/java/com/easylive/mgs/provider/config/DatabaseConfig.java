package com.easylive.mgs.provider.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;

@Configuration
public class DatabaseConfig {

    private static final String ENTITY_MANAGER_PACKAGES_TO_SCAN = "com.easylive.mgs.provider.entity";
    @Value("${hibernate.show_sql:false}")
    private String hibernateShowSql;

    @Bean(name = "master")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource primaryDataSource() {
        return DataSourceBuilder.create().build();
    }


    @Bean(name = "entityManagerFactory")
    @Primary
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(primaryDataSource())
                .properties(getVendorProperties())
                .packages(ENTITY_MANAGER_PACKAGES_TO_SCAN)
                .build();
    }

    @Primary
    @Bean
    public PlatformTransactionManager transactionManager(EntityManagerFactoryBuilder builder) throws IOException {
        return new JpaTransactionManager(entityManagerFactory(builder).getObject());
    }


    @Bean(name = "slave")
    @ConfigurationProperties(prefix = "spring.datasource.secondary")
    public DataSource secondaryDataSource() {
        return DataSourceBuilder.create().build();
    }


    @Bean(name = "readEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean readEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(secondaryDataSource())
                .properties(getVendorProperties())
                .packages(ENTITY_MANAGER_PACKAGES_TO_SCAN)
                .build();
    }

    @Bean
    public PlatformTransactionManager readTransactionManager(@Qualifier("readEntityManagerFactory") LocalContainerEntityManagerFactoryBean readEntityManagerFactory) {
        return new JpaTransactionManager(readEntityManagerFactory.getObject());
    }



    @Bean(name = "provider")
    @ConfigurationProperties(prefix = "spring.datasource.provider")
    public DataSource providerDataSource() {
        return DataSourceBuilder.create().build();
    }


    @Bean(name = "providerEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean providerEntityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(providerDataSource())
                .properties(getVendorProperties())
                .packages(ENTITY_MANAGER_PACKAGES_TO_SCAN)
                .build();
    }

    @Bean
    public PlatformTransactionManager providerTransactionManager(@Qualifier("providerEntityManagerFactory") LocalContainerEntityManagerFactoryBean providerEntityManagerFactory) {
        return new JpaTransactionManager(providerEntityManagerFactory.getObject());
    }

    protected Map<String, Object> getVendorProperties() {
        Map<String, Object> vendorProperties = new LinkedHashMap<>();
        vendorProperties.put("hibernate.show_sql", this.hibernateShowSql);
        return vendorProperties;
    }

    @EnableJpaRepositories(basePackages = "com.easylive.mgs.provider.repo.main",
            entityManagerFactoryRef = "entityManagerFactory", transactionManagerRef = "transactionManager")
    @Primary
    public class WriteConfiguration {
    }

    @EnableJpaRepositories(basePackages = "com.easylive.mgs.provider.repo.read",
            entityManagerFactoryRef = "readEntityManagerFactory", transactionManagerRef = "readTransactionManager")
    public class ReadConfiguration {
    }


    @EnableJpaRepositories(basePackages = "com.easylive.mgs.provider.repo.provider",
            entityManagerFactoryRef = "providerEntityManagerFactory", transactionManagerRef = "providerTransactionManager")
    public class ProviderConfiguration {
    }

} // class DatabaseConfig

