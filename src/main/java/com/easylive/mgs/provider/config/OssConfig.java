package com.easylive.mgs.provider.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
public class OssConfig {

    @Value("${oss.porn.access.key.id}")
    private String pornAccessKeyId;

    @Value("${oss.porn.access.key.secret}")
    private String pornAccessKeySecret;

    @Value("${oss.porn.region}")
    private String pornRegion;

    @Value("${oss.porn.endpoint}")
    private String pornEndpoint;

    @Value("${oss.auth.endpoint}")
    private String authEndpoint;

    @Value("${oss.porn.video.endpoint}")
    private String pronVideoEndpoint;

    @Value("${oss.porn.video.seed}")
    private String pronVideoSeed;

    @Value("${oss.porn.video.callback}")
    private String pronVideoCallback;

    @Value("${oss.access.uid}")
    private String accessUid;
}
