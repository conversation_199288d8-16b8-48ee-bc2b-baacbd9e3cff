package com.easylive.mgs.provider.common;

import java.math.RoundingMode;

/**
 * 定义项目中的公共全局常量，最好是多个地方需要使用的。
 * 如果只是一个模块使用的，且不公开使用，定义在各自的类里面.
 */
public class Constants {

    public static final int CONSTANTS_1 = 9;
    public static final RoundingMode GLOBAL_ROUNDING_MODE = RoundingMode.HALF_UP;

    public static final int HTTP_RESPONSE_OK = 200;
    public static final String HTTP_PREFIX = "http://";
    public static final String HTTP_SCHEME = "http";


    public static final String DEFAULT_USER_AGENT = "EasyLive MGS HTTP Client Provider";
}
