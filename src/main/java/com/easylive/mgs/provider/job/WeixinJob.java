package com.easylive.mgs.provider.job;

import com.easylive.mgs.provider.service.weixin.WeixinService;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;


@Slf4j
@Service
public class Wei<PERSON><PERSON>ob extends ZkBaseJob {

    private final static String name = "weixin-access-token";

    @Value("${weixin.token.service.enabled:false}")
    private boolean accessTokenServiceEnabled;

    @Autowired
    private WeixinService weixinService;

    public WeixinJob() {
        super(name);
    }

    @Autowired
    private CuratorFramework curator;

    @PostConstruct
    private void init() {
        this.init(curator);
    }

    @PreDestroy
    private void destroy() {
    }

    @Scheduled(initialDelay =  1000L, fixedDelay = 60 * 1000L)
    private void execute() {
        // log.info("Start to execute");
        if( accessTokenServiceEnabled )
            this.execute(this::reloadWeixinAccessToken, 30000);
        // log.info("Start to execute");
        this.execute(this::reloadWeixinAccessToken, 30000);
    }

    private void reloadWeixinAccessToken() {
        // log.info("refresh weixin access token");
        weixinService.reload();
    }

}
