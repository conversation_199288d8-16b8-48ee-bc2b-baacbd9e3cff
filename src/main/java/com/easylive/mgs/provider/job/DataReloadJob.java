package com.easylive.mgs.provider.job;

import com.easylive.mgs.provider.service.CDNDomainService;
import com.easylive.mgs.provider.service.CloudProviderService;
import com.easylive.mgs.provider.service.PornService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/7/3
 */
@Slf4j
@Service
public class DataReloadJob {

    @Autowired
    private CDNDomainService cdnDomainService;

    @Autowired
    private CloudProviderService cloudProviderService;

    @Autowired
    private PornService pornService;

    @Scheduled(initialDelay =  3000L, fixedDelay = 60 * 10 * 1000L)
    public void reloadCloudProvider() {
        cloudProviderService.load();
    }

    @Scheduled(initialDelay =  5000L, fixedDelay = 60 * 5 * 1000L)
    public void reloadCDN() {
        cdnDomainService.load();
    }

    @Scheduled(initialDelay =  5000L, fixedDelay = 60 * 1 * 1000L)
    public void reloadCDNSched() {
        cdnDomainService.loadSched();
    }


    @Scheduled(initialDelay = 60 * 1000L, fixedDelay = 3600 * 1000L)
    public void reloadWords() {
        pornService.init();
    }

    @Scheduled(cron = "0 30 7 * * ?")
    public void removeExpiredWords() {
        pornService.removeExpiredWords();
    }
}
