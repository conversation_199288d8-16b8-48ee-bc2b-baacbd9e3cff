package com.easylive.mgs.provider.job;

import com.easylive.common.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.leader.LeaderLatch;
import org.apache.curator.framework.recipes.leader.LeaderLatchListener;

import java.io.IOException;
import java.util.UUID;

@Slf4j
public class ZkBaseJob {

    private final static String ELECTION_ROOT_PATH = "/cron/election/";
    private String name;
    private LeaderLatch leaderLatch;
    private JobState state = JobState.CREATED;
    private final static int ElectionTimeOut = 4;

    public enum JobState {
        /*
        创建
         */
        CREATED,
        ELECTING,
        SLEEPING,
        RUNNING,
        STOPPED
    }

    protected ZkBaseJob(String name) {
        this.name = name;
    }

    protected void execute(Runnable runnable) {
        this.execute(runnable, -1);
    }
    protected void execute(Runnable runnable, long timeout) {
        long start = System.currentTimeMillis();
        while (state != JobState.SLEEPING) {
            SystemUtil.sleep(1000);
            if( timeout > 0 && (System.currentTimeMillis() - start) > timeout)
                break;
        }

        if( state == JobState.SLEEPING ) {
            state = JobState.RUNNING;
            try {
                runnable.run();
            } catch (Exception e) {
                log.error("Error running job", e);
            }
            state = JobState.SLEEPING;
        }
    }

    /**
     * 选举leader
     * @param curator
     */
    protected void init(CuratorFramework curator) {
        String path = ELECTION_ROOT_PATH + name;
        leaderLatch = new LeaderLatch(curator, path);
        leaderLatch.addListener(new LeaderLatchListener() {
            @Override
            public void isLeader() {
                log.info("[ExclusiveJob] Job leader election success : {}, {}", name, UUID.randomUUID().toString());
                state = JobState.SLEEPING;
            }

            @Override
            public void notLeader() {
                log.info("[ExclusiveJob] Job leader election not success: {}, {}", name, UUID.randomUUID().toString());
                if( state == JobState.RUNNING || state == JobState.SLEEPING) {
                    log.info("[ExclusiveJob] losing leadership, job will not execute next time");
                }
                state = JobState.ELECTING;
            }
        });
        start();

    }

    protected void start() {
        log.info("Start job {}", name);
        this.state = JobState.ELECTING;
        try {
            log.info("Joining job leader election: {}", name);
            leaderLatch.start();
        } catch (Exception e) {
            log.error("Error start job leader election: {}", name);
        }
    }

    protected void stop() {
        this.state = JobState.STOPPED;
        try {
            leaderLatch.close();
        } catch (IOException e) {
            log.error("error close curator", e);
        }
    }
}
