package com.easylive.mgs.provider.job;

import com.easylive.mgs.provider.service.CDNDomainService;
import com.easylive.mgs.provider.service.weixin.WeixinService;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;


@Slf4j
@Service
public class CDNDomainJob extends ZkBaseJob {

    private final static String name = "cdn-refresh-task";

    @Autowired
    private CDNDomainService cdnDomainService;

    public CDNDomainJob() {
        super(name);
    }

    @Autowired
    private CuratorFramework curator;

    @PostConstruct
    private void init() {
        this.init(curator);
    }

    @PreDestroy
    private void destroy() {
    }

    @Scheduled(initialDelay =  5000L, fixedDelay = 5 * 1000L)
    private void execute() {
        // log.info("Start to execute");
        this.execute(this::processRefreshTask, 30000);
    }

    private void processRefreshTask() {
        cdnDomainService.processRefreshTask();
    }

}
