package com.easylive.mgs.provider.service.porn.impl;

import com.aliyun.green20220302.models.VideoModerationResponseBody;
import com.easylive.common.util.DateTimeUtil;
import com.easylive.mgs.provider.entity.TencentPornEntity;
import com.easylive.mgs.provider.enums.PornBizTypeEnum;
import com.easylive.mgs.provider.enums.PornTaskStatusEnum;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.model.PronScanDTO;
import com.easylive.mgs.provider.repo.main.TencentPornRepository;
import com.easylive.mgs.provider.repo.read.TencentPornReadRepository;
import com.easylive.mgs.provider.service.RedisService;
import com.easylive.mgs.provider.service.porn.PornService;
import com.easylive.mgs.provider.service.porn.abstracts.AbstractVideoPorn;
import com.easylive.rpc.http.ResponseException;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("VideoPornService")
public class VideoPornServiceImpl implements PornService {

    @Autowired
    private TencentPornReadRepository tencentPornReadRepository;

    @Autowired
    private TencentPornRepository tencentPornRepository;

    @Autowired
    private RedisService redisService;

    @Autowired
    private AbstractVideoPorn videoPorn;

    /**
     * 创建订单
     * @param bizId
     * @param content
     * @return
     */
    @Override
    public PronScanDTO createTask(String bizId, String content) {

        String cacheKey = "Ali:scan:video:task:" + DateTimeUtil.formatAsDate(new Date());
        Long value = redisService.incrementAndExpire(cacheKey, 1L, 86400 * 1000L);

        if (value > 1000L) {
            throw new ResponseException(ProviderError.E_ALI_SCAN_TASK_LIMIT);
        }

        TencentPornEntity entity = tencentPornReadRepository.findTopByBizIdAndBizTypeOrderById(bizId, PornBizTypeEnum.VIDEO.getType());
        if (entity != null) {
            throw new ResponseException(ProviderError.E_ALI_TASK_EXISTS);
        }

        VideoModerationResponseBody.VideoModerationResponseBodyData response = videoPorn.createTask(bizId, content);
        if (response == null) {
            log.error("Create ali video porn task result is empty. {}", bizId);
            throw new ResponseException(ProviderError.E_ALI_CREATE);
        }

        //写入数据库
        if (Strings.isNotBlank(response.getTaskId())) {
            TencentPornEntity createEntity = TencentPornEntity.builder()
                    .bizId(bizId)
                    .bizType(PornBizTypeEnum.VIDEO.getType())
                    .taskId(response.getTaskId())
                    .taskStatus(PornTaskStatusEnum.CREATE.getStatus())
                    .taskContent(content).build();
            tencentPornRepository.save(createEntity);
        }

        return new PronScanDTO(response.getTaskId(), bizId);
    }
}
