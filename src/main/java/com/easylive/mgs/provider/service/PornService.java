package com.easylive.mgs.provider.service;

import cn.hutool.dfa.FoundWord;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.green20220302.Client;
import com.aliyun.green20220302.models.*;
import com.aliyun.teautil.models.RuntimeOptions;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.green.model.v20180509.ImageSyncScanRequest;
import com.aliyuncs.green.model.v20180509.TextScanRequest;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.easylive.common.util.DateTimeUtil;
import com.easylive.common.util.JsonUtil;
import com.easylive.common.util.dfa.SensitiveWordUtil;
import com.easylive.mgs.provider.common.Constants;
import com.easylive.mgs.provider.config.OssConfig;
import com.easylive.mgs.provider.entity.SensitiveWordsEntity;
import com.easylive.mgs.provider.enums.SceneType;
import com.easylive.mgs.provider.model.GreenImageScanResult;
import com.easylive.mgs.provider.model.ModelFactory;
import com.easylive.mgs.provider.model.ali.AliSensitiveTips;
import com.easylive.mgs.provider.pub.enums.DetectionServiceEnum;
import com.easylive.mgs.provider.pub.enums.ImageModerationEnum;
import com.easylive.mgs.provider.pub.enums.SuggestionType;
import com.easylive.mgs.provider.pub.model.ScanDTO;
import com.easylive.mgs.provider.repo.provider.SensitiveWordsRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.ehcache.Cache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Executor;

@Service
@Slf4j
public class PornService {

    @Autowired
    private OssConfig ossConfig;

    @Autowired
    private SensitiveWordsRepository sensitiveWordsRepository;

    private static Set<String> NORMAL_WORDS = new HashSet<>();
    private static HashMap<String, ScanDTO> SENSITIVE_WORDS_TIPS = new HashMap<>();

    @Autowired
    @Qualifier("scanThreadPool")
    private Executor taskExecutor;

    @Resource(name = "TextCache")
    private Cache<String, GreenImageScanResult> textCache;

    @Resource(name = "TextEnhanceCache")
    private Cache<String, ScanDTO> textEnhanceCache;

    @PostConstruct
    public void init() {
        initSensitiveWords();
        initNormalWords();
    }

    private List<String> SCENES = Arrays.asList(SceneType.AD.getValue(), SceneType.PORN.getValue(), SceneType.TERRORISM.getValue(), SceneType.QRCODE.getValue());

    public GreenImageScanResult imageCheck(String imageUrl) {
        return imageScan(imageUrl, SCENES);
    }

    public GreenImageScanResult aliPorn(String imageUrl) {
        return imagePornCheck(imageUrl);
    }

    public GreenImageScanResult imagePornCheck(String imageUrl) {
        return imageScan(imageUrl, Collections.singletonList(SceneType.PORN.getValue()));
    }

    public GreenImageScanResult imageAdCheck(String imageUrl) {
        return imageScan(imageUrl, Collections.singletonList(SceneType.AD.getValue()));
    }

    public GreenImageScanResult imageScan(String imageUrl, List<String> scenes) {

        //请替换成你自己的accessKeyId、accessKeySecret
        IClientProfile profile = DefaultProfile.getProfile(ossConfig.getPornRegion(), ossConfig.getPornAccessKeyId(), ossConfig.getPornAccessKeySecret());
        DefaultProfile.addEndpoint(ossConfig.getPornRegion(), "Green", ossConfig.getPornEndpoint());

        IAcsClient client = new DefaultAcsClient(profile);

        ImageSyncScanRequest imageSyncScanRequest = new ImageSyncScanRequest();
        // 指定api返回格式
        imageSyncScanRequest.setSysAcceptFormat(FormatType.JSON);
        // 指定请求方法
        imageSyncScanRequest.setMethod(com.aliyuncs.http.MethodType.POST);
        imageSyncScanRequest.setEncoding("utf-8");
        imageSyncScanRequest.setRegionId(ossConfig.getPornRegion());

        List<Map<String, Object>> tasks = new ArrayList<>();
        Map<String, Object> task = new LinkedHashMap<>();
        task.put("dataId", UUID.randomUUID().toString());
        log.info("[imageScan] 1. dataId={}", task.get("dataId"));
        task.put("url", imageUrl);
        task.put("time", new Date());

        tasks.add(task);
        JSONObject data = new JSONObject();
        data.put("scenes", scenes);
        data.put("tasks", tasks);

        imageSyncScanRequest.setHttpContent(data.toJSONString().getBytes(StandardCharsets.UTF_8), "UTF-8", FormatType.JSON);

        /**
         * 请务必设置超时时间
         */
        imageSyncScanRequest.setConnectTimeout(3000);
        imageSyncScanRequest.setReadTimeout(3000);

        try {
            HttpResponse httpResponse = client.doAction(imageSyncScanRequest, false, 1);

            if (httpResponse.isSuccess()) {

                JSONObject scrResponse = JSON.parseObject(new String(httpResponse.getHttpContent(), StandardCharsets.UTF_8));
                log.info("[imageScan] 1. scrResponse={}", JsonUtil.toString(scrResponse));


                if (Constants.HTTP_RESPONSE_OK == scrResponse.getInteger("code")) {
                    JSONArray taskResults = scrResponse.getJSONArray("data");


                    for (Object taskResult : taskResults) {
                        if (Constants.HTTP_RESPONSE_OK == ((JSONObject) taskResult).getInteger("code")) {
                            JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");

                            GreenImageScanResult result = new GreenImageScanResult();
                            Integer tmpLevel = 0;

                            for (Object sceneResult : sceneResults) {
                                log.info("[imageScan] 2. sceneResult = {}", sceneResult);

                                JSONObject sceneObject = (JSONObject) sceneResult;
                                String suggestion = sceneObject.getString("suggestion");
                                Float rate = sceneObject.getFloat("rate");
                                Integer level = SuggestionType.ofOrdinal(suggestion);

                                if (result.getScene() == null || level > tmpLevel) {
                                    result = ModelFactory.getScanResultFromJsonObject(sceneObject);
                                    tmpLevel = level;
                                    continue;
                                }

                                if (level.equals(tmpLevel) && result.getRate() < rate) {
                                    result = ModelFactory.getScanResultFromJsonObject(sceneObject);
                                    tmpLevel = level;
                                }
                             }

                            return result;
                        } else {
                            log.error("[imageScan] task process fail:" + ((JSONObject) taskResult).getInteger("code"));
                        }
                    }
                } else {
                    log.error("[imageScan] detect not success. code:" + scrResponse.getInteger("code"));
                }
            } else {
                log.error("[imageScan] response not success. status:" + httpResponse.getStatus());
            }
        } catch (ServerException e) {
            log.error("[imageScan] ServerException. ", e);
        } catch (ClientException e) {
            log.error("[imageScan] ClientException. ", e);
        } catch (Exception e) {
            log.error("[imageScan] Exception. ", e);
        }

        return new GreenImageScanResult();
    }


    public GreenImageScanResult textScan(String content) {
        log.info("[textScan]:  {}", content);
        GreenImageScanResult result = textCache.get(content);
        if( result == null ) {
            result = internalTextScan(content);
            if( result.getLabel() != null ) {
                textCache.put(content, result);
            }
        } else {
            log.info("[textScan][Cache Hit]: {}", JsonUtil.toString(result));
        }
        return result;
    }


    public ScanDTO textEnhanceScan(String content, DetectionServiceEnum detectionService) {
        ScanDTO result = textEnhanceCache.get(content);
        if (result != null) {
            log.info("[textEnhanceScan][Cache Hit]: {}", JsonUtil.toString(result));
            return result;
        }

        if (checkNormalWord(content)) {
            return new ScanDTO(detectionService.name(), SuggestionType.PASS, 99.9f);
        }

        ScanDTO scanResult = checkSensitiveWord(content);
        if (scanResult.getSuggestion() == SuggestionType.BLOCK) {
            scanResult.setScene(detectionService.name());
            return scanResult;
        }

        // 调用阿里的接口
        result = internalTextScan(content, detectionService);
        textEnhanceCache.put(content, result);

        //入库
        ScanDTO finalResult = result;
        if(Strings.isNotBlank(result.getLabels())) {
            taskExecutor.execute(() -> saveSensitiveWord(finalResult, content));
        } else {
            taskExecutor.execute(() -> saveNormalWord(finalResult, content));
        }
        return result;
    }

    @Autowired
    @Qualifier("AliScanTextClient")
    private Client client;

    public ScanDTO internalTextScan(String content, DetectionServiceEnum detectionService) {
        RuntimeOptions runtime = new RuntimeOptions();
        runtime.readTimeout = 10000;
        runtime.connectTimeout = 10000;

        JSONObject serviceParameters = new JSONObject();
        serviceParameters.put("content", content);

        ScanDTO scanDTO = new ScanDTO();

        TextModerationRequest textModerationRequest = new TextModerationRequest();
        textModerationRequest.setService(detectionService.getService());
        textModerationRequest.setServiceParameters(serviceParameters.toJSONString());
        try {
            // 调用方法获取检测结果。
            TextModerationResponse response = client.textModerationWithOptions(textModerationRequest, runtime);

            // 打印检测结果。
            if (response == null) {
                log.error("InternalTextScan response is empty.");
                return scanDTO;
            }

            if (response.getStatusCode() != 200) {
                log.error("InternalTextScan response not success. status: {}", response.getStatusCode());
                return scanDTO;
            }


            TextModerationResponseBody result = response.getBody();
            Integer code = result.getCode();
            if (code == null || code != 200) {
                log.error("InternalTextScan text moderation not success. code not eq 200, result:{}", JsonUtil.toString(result));
                return scanDTO;
            }

            TextModerationResponseBody.TextModerationResponseBodyData data = result.getData();
            log.info("Text scan ali result:{}", JsonUtil.toString(data));

            String labels = data.getLabels();
            SuggestionType suggestionType = Strings.isBlank(labels) ? SuggestionType.PASS : SuggestionType.BLOCK;

            scanDTO.setScene(detectionService.name());
            scanDTO.setSuggestion(suggestionType);
            scanDTO.setRate(99.9f);
            scanDTO.setTips(data.getReason());
            scanDTO.setLabels(labels);
            return scanDTO;
        } catch (Exception e) {
            log.error("InternalTextScan request error.", e);
        }

        return scanDTO;
    }


    private GreenImageScanResult internalTextScan(String content) {
        //请替换成你自己的accessKeyId、accessKeySecret
        IClientProfile profile = DefaultProfile.getProfile(ossConfig.getPornRegion(), ossConfig.getPornAccessKeyId(), ossConfig.getPornAccessKeySecret());
        DefaultProfile.addEndpoint(ossConfig.getPornRegion(), "Green", ossConfig.getPornEndpoint());

        IAcsClient client = new DefaultAcsClient(profile);

        TextScanRequest textScanRequest = new TextScanRequest();
        // 指定api返回格式
        textScanRequest.setAcceptFormat(FormatType.JSON);
        // 指定请求方法
        textScanRequest.setMethod(com.aliyuncs.http.MethodType.POST);
        textScanRequest.setEncoding("UTF-8");
        textScanRequest.setRegionId(ossConfig.getPornRegion());

        List<Map<String, Object>> tasks = new ArrayList<>();
        Map<String, Object> task = new LinkedHashMap<>();
        task.put("dataId", UUID.randomUUID().toString());
        log.info("[textScan] Request text scan service. 1. dataId={}", task.get("dataId"));
        task.put("content", content);

        tasks.add(task);
        JSONObject data = new JSONObject();
        data.put("scenes", Arrays.asList("antispam"));
        data.put("tasks", tasks);


        //log.debug("json content:{}", JSON.toJSONString(data, true));

        textScanRequest.setHttpContent(data.toJSONString().getBytes(StandardCharsets.UTF_8), "UTF-8", FormatType.JSON);

        /**
         * 请务必设置超时时间
         */
        textScanRequest.setConnectTimeout(3000);
        textScanRequest.setReadTimeout(3000);

        try {
            HttpResponse httpResponse = client.doAction(textScanRequest, false, 1);

            if (httpResponse.isSuccess()) {

                JSONObject scrResponse = JSON.parseObject(new String(httpResponse.getHttpContent(), StandardCharsets.UTF_8));
                log.info("[textScan] 1. scrResponse={}", JsonUtil.toString(scrResponse));

                if (Constants.HTTP_RESPONSE_OK == scrResponse.getInteger("code")) {
                    JSONArray taskResults = scrResponse.getJSONArray("data");
                    for (Object taskResult : taskResults) {
                        if (Constants.HTTP_RESPONSE_OK == ((JSONObject) taskResult).getInteger("code")) {
                            JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");
                            for (Object sceneResult : sceneResults) {
                                log.info("[textScan] 2. sceneResult = {}", sceneResult);

                                JSONObject sceneObject = (JSONObject) sceneResult;
                                String scene = sceneObject.getString("scene");
                                if (scene.equals(SceneType.ANTISPAM.getValue())) {
                                    return ModelFactory.getScanResultFromJsonObject(sceneObject);
                                }
                            }
                        } else {
                            log.error("[textScan] task process fail:" + ((JSONObject) taskResult).getInteger("code"));
                        }
                    }
                } else {
                    log.error("[textScan] detect not success. code:" + scrResponse.getInteger("code"));
                }
            } else {
                log.error("[textScan] response not success. status:" + httpResponse.getStatus());
            }
        } catch (ServerException e) {
            log.error("[textScan] ServerException. ", e);
        } catch (ClientException e) {
            log.error("[textScan] ClientException. ", e);
        } catch (Exception e) {
            log.error("[textScan] Exception. ", e);
        }

        return new GreenImageScanResult();
    }
    /**
     * 图片增强鉴定（OSS）
     * @param fileName 图片名称
     * @param imageModerationEnum 增强服务
     * @param ossModel bucket类型
     * @param dataId 业务数据id
     * @return
     */
    public GreenImageScanResult imageScanEnhanceFormOss(String fileName, ImageModerationEnum imageModerationEnum,  String ossModel, String dataId){
        Map<String, String> map = new HashMap<>();
        // 待检测文件所在bucket的区域。 示例：cn-shanghai
        map.put("ossRegionId", ossConfig.getPornRegion());
        // 待检测文件所在bucket名称。示例：bucket001
        map.put("ossBucketName", ossModel);
        // 待检测文件。 示例：image/001.jpg
        map.put("ossObjectName", fileName);
        if (!StringUtils.isEmpty(dataId)){
            // 待检测数据唯一标识
            map.put("dataId", dataId);
        }
        return requestImageScan(imageModerationEnum, JSON.toJSONString(map));
    }

    /**
     * 图片增强鉴定
     * @param imageUrl 图片公网地址
     * @param imageModerationEnum 增强服务
     * @param dataId 业务数据id
     * @return
     */
    public GreenImageScanResult imageScanEnhance(String imageUrl, ImageModerationEnum imageModerationEnum, String dataId){
        //构建参数
        Map<String, String> map = new HashMap<>();
        // 待检测文件公网地址。
        map.put("imageUrl", imageUrl);
        if (!StringUtils.isEmpty(dataId)){
            // 待检测数据唯一标识
            map.put("dataId", dataId);
        }
        return requestImageScan(imageModerationEnum , JSON.toJSONString(map));
    }

    /**
     * 图片增强鉴定请求解析
     * @param imageModerationEnum 增强服务
     * @param requestParams 请求参数
     * @return
     */
    public GreenImageScanResult requestImageScan(ImageModerationEnum imageModerationEnum, String requestParams){
        //构建请求
        ImageModerationRequest request = new ImageModerationRequest();
        request.setService(imageModerationEnum.getService());
        request.setServiceParameters(requestParams);

        RuntimeOptions runtimeOptions = new RuntimeOptions();
        runtimeOptions.connectTimeout = 10000;
        runtimeOptions.readTimeout = 10000;

        //解析response
        ImageModerationResponse response = null;
        try {
            response = client.imageModerationWithOptions(request, runtimeOptions);
        } catch (Exception e) {
            log.error("[PornService][requestImageScan] image scan request fail. ", e);
        }
        GreenImageScanResult scanDTO = new GreenImageScanResult();
        scanDTO.setScene(imageModerationEnum.name());
        if(response == null || response.getStatusCode() != 200){
            log.error("[PornService][requestImageScan] response not success, response: {}", JsonUtil.toString(response));
            return scanDTO;
        }
        ImageModerationResponseBody body = response.getBody();
        if (null == body.getCode() || body.getCode() != 200){
            log.error("[PornService][requestImageScan] response body is error, body:{}", JsonUtil.toString(body));
            return scanDTO;
        }
        ImageModerationResponseBody.ImageModerationResponseBodyData data = body.getData();
        List<ImageModerationResponseBody.ImageModerationResponseBodyDataResult> results = data.getResult();
        log.info("[PornService][requestImageScan] response results=> {}", JSON.toJSONString(results));
        scanDTO.setDataId(data.getDataId());
        if (CollectionUtils.isEmpty(results)){
            return scanDTO;
        }
        SuggestionType suggestionType = SuggestionType.REVIEW;
        for (ImageModerationResponseBody.ImageModerationResponseBodyDataResult moderation : results) {
            if ("nonLabel".equals(moderation.getLabel()) || "nonLabel_lib".equals(moderation.getLabel())){
                suggestionType = SuggestionType.PASS;
                scanDTO.setLabel(moderation.getLabel());
                break;
            }else if (moderation.getConfidence() > 90){
                suggestionType = SuggestionType.BLOCK;
            }
        }
        scanDTO.setSuggestion(suggestionType.getValue());
        if (suggestionType != SuggestionType.PASS){
            Optional<ImageModerationResponseBody.ImageModerationResponseBodyDataResult> first = results.stream()
                    .max(Comparator.comparing(ImageModerationResponseBody.ImageModerationResponseBodyDataResult::getConfidence));
            if (first.isPresent()){
                final ImageModerationResponseBody.ImageModerationResponseBodyDataResult moderation = first.get();
                scanDTO.setLabel(moderation.getLabel());
                scanDTO.setRate(moderation.getConfidence());
            }
        }
        return scanDTO;
    }

    public void saveSensitiveWord(ScanDTO data, String content) {
        try {
            String tips = data.getTips();
            AliSensitiveTips aliSensitiveTips = JsonUtil.fromString(tips, AliSensitiveTips.class);
            if (aliSensitiveTips == null) {
                return;
            }

            String riskWords = aliSensitiveTips.getRiskWords();
            if (StringUtils.isEmpty(riskWords)) {
                riskWords = content;
            }

            addSensitiveWords(riskWords, data);
            saveWord(riskWords, data);

        } catch (Exception e) {
            log.error("Save sensitive word error. data:{}", data, e);
        }
    }

    public void saveNormalWord(ScanDTO data, String content) {
        try {
            if (Strings.isBlank(content) || content.length() > 20) {
                return;
            }

            addNormalWords(content);
            saveWord(content, data);

        } catch (Exception e) {
            log.error("Save sensitive word error. data:{}", data, e);
        }
    }

    /**
     * 保存检查结果词汇
     * @param words
     * @param data
     */
    private void saveWord(String words, ScanDTO data) {
        boolean saved = sensitiveWordsRepository.existsByWords(words);
        if (saved) {
            return;
        }

        SensitiveWordsEntity entity = new SensitiveWordsEntity();
        entity.setLabels(data.getLabels());
        entity.setWords(words);
        entity.setTips(data.getTips());
        entity.setSuggestion(data.getSuggestion().name());
        entity.setCreateTime(new Date());
        entity.setUpdatedTime(new Date());
        sensitiveWordsRepository.save(entity);
    }

    /**
     * 检查敏感词
     * @param content
     * @return
     */
    public ScanDTO checkSensitiveWord(String content) {
        final List<FoundWord> foundAllSensitive = SensitiveWordUtil.getFoundAllSensitive(content);
        log.info("Check sensitive words : {}, {}", content, foundAllSensitive);
        if (!CollectionUtils.isEmpty(foundAllSensitive)) {
            return SENSITIVE_WORDS_TIPS.get(foundAllSensitive.get(0).getWord());
        }
        return new ScanDTO();
    }

    /**
     * 检查正常词
     * @param word
     * @return
     */
    public boolean checkNormalWord(String word) {
        boolean isNormal = NORMAL_WORDS.contains(word);
        log.info("Check normal words : {}, {}", word, isNormal);
        return isNormal;
    }

    /**
     * 初始化敏感词
     */
    public void initSensitiveWords() {
        List<SensitiveWordsEntity> sensitiveWords = sensitiveWordsRepository.listBySuggestion(SuggestionType.BLOCK.name());
        if (CollectionUtils.isEmpty(sensitiveWords)) {
            SensitiveWordUtil.init(List.of());
            return;
        }

        Set<String> sensitiveWordSet = new HashSet<>();
        sensitiveWords.forEach(words -> {
            //getWords是以逗号隔开的
            String[] wordsArray = words.getWords().split(",");
            sensitiveWordSet.addAll(List.of(wordsArray));

            for (String wd : wordsArray) {
                SENSITIVE_WORDS_TIPS.put(wd, new ScanDTO(words.getLabels(), SuggestionType.BLOCK, 99.9f, words.getTips()));
            }
        });
        log.info("Init sensitive words:{}", sensitiveWordSet);
        SensitiveWordUtil.init(sensitiveWordSet);
    }

    /**
     * 初始化正常词汇，定时清理
     */
    public void initNormalWords() {
        List<String> normalWords = sensitiveWordsRepository.listWordsBySuggestion(SuggestionType.PASS.name());
        NORMAL_WORDS.clear();
        if (CollectionUtils.isEmpty(normalWords)) {
            return;
        }
        NORMAL_WORDS.addAll(normalWords);
        log.info("Init normal words:{}", normalWords);
    }

    /**
     * 新增敏感词
     * @param sensitiveWords
     */
    public void addSensitiveWords(String sensitiveWords, ScanDTO data) {
        if (StringUtils.isEmpty(sensitiveWords)) {
            return;
        }

        String[] words = sensitiveWords.split(",");

        for (String wd : words) {
            SENSITIVE_WORDS_TIPS.put(wd, data);
        }

        SensitiveWordUtil.addWord(Arrays.asList(words));
        log.info("Add sensitive words:{}", sensitiveWords);
    }

    /**
     * 添加正常词汇
     * @param word
     */
    public void addNormalWords(String word) {
        if (StringUtils.isEmpty(word)) {
            return;
        }

        NORMAL_WORDS.add(word);
        log.info("Add normal words:{}", word);
    }

    /**
     * 清理过去一个月词语
     */
    public void removeExpiredWords() {
        Date expiredDate = DateTimeUtil.oneDate(-30);
        sensitiveWordsRepository.delExpiredWords(expiredDate);
    }


}
