package com.easylive.mgs.provider.service.cdn;

import com.aliyun.cdn20180510.Client;
import com.aliyun.cdn20180510.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.easylive.common.util.JsonUtil;
import com.easylive.mgs.provider.enums.CDNRefreshStatus;
import com.easylive.mgs.provider.pub.enums.CDNRefreshType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URL;
import java.time.Instant;
import java.util.Date;
import java.util.List;

@Slf4j
public class AliyunCDNProvider implements CDNProvider {
    private Client client = null;
    private final RuntimeOptions runtimeOptions;

    public AliyunCDNProvider(String accessKeyId, String accessKeySecret) {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret)
                .setEndpoint("cdn.aliyuncs.com");
        try {
            client = new Client(config);
        } catch (Exception e) {
            log.error("init aliyun cdn client error", e);
        }
        runtimeOptions = new RuntimeOptions();
    }

    @Override
    public String refresh(String[] urls, CDNRefreshType type, boolean force) throws Exception {
        RefreshObjectCachesRequest request = new RefreshObjectCachesRequest();
        request.setObjectType(type.getName());
        request.setObjectPath(String.join("\n", urls));
        request.setForce(force);
        RefreshObjectCachesResponse response = client.refreshObjectCachesWithOptions(request, runtimeOptions);
        log.info("refresh response:{}, {}", response.getStatusCode(), response.getBody());
        return response.getBody().getRefreshTaskId();
    }

    @Override
    public String preload(String[] urls) throws Exception {
        PushObjectCacheRequest request = new PushObjectCacheRequest();
        request.setObjectPath(String.join("\n", urls));
        PushObjectCacheResponse response = client.pushObjectCacheWithOptions(request, runtimeOptions);
        return response.getBody().getPushTaskId();
    }

    @Override
    public CDNRefreshTask checkStatus(String domain, String taskId) throws Exception {
        DescribeRefreshTaskByIdRequest request = new DescribeRefreshTaskByIdRequest();
        request.setTaskId(taskId);
        DescribeRefreshTaskByIdResponse response = client.describeRefreshTaskByIdWithOptions(request,runtimeOptions);
        if( response != null  && response.getBody() != null ) {
            Long count = response.getBody().getTotalCount();
            if( count == 0 ) {
                return null;
            }

            if( count > 1 ) {
                log.warn("More than one tasks count:{}", count);
            }

            DescribeRefreshTaskByIdResponseBody.DescribeRefreshTaskByIdResponseBodyTasks task = response.getBody().getTasks().get(0);
            CDNRefreshTask refreshTask = new CDNRefreshTask();
            refreshTask.setTaskId(task.getTaskId());
            refreshTask.setStatus( mapStatus(task.getStatus()));

            URL url = new URL(task.getObjectPath());
            refreshTask.setDomain( url.getHost() );
            refreshTask.setPath( url.getPath() );
            refreshTask.setType(mapType(task.getObjectType()));
            refreshTask.setCreateTime(Date.from(Instant.parse(task.getCreationTime())));
            refreshTask.setErrorMsg(task.getDescription());
            refreshTask.setProcess(task.getProcess());

            return refreshTask;
        }
        return null;
    }

    private CDNRefreshStatus mapStatus(String status) {
        CDNRefreshStatus cdnRefreshStatus = CDNRefreshStatus.fromName(status.toLowerCase());
        if (cdnRefreshStatus == null)
            return CDNRefreshStatus.UNKNOWN;
        return cdnRefreshStatus;
    }
    private CDNRefreshType mapType(String type) {
        return  CDNRefreshType.fromName(type);
    }
}
