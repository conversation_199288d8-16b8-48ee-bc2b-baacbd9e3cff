package com.easylive.mgs.provider.service.cdn;

import com.easylive.mgs.provider.enums.CDNRefreshStatus;
import com.easylive.mgs.provider.pub.enums.CDNRefreshType;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/5
 */
@Data
public class CDNRefreshTask {
    private String taskId;
    private String domain;
    private String path;
    private CDNRefreshType type;
    private CDNRefreshStatus status;
    private Date createTime;
    private Date completeTime;
    private String process;
    private String errorMsg;
}
