package com.easylive.mgs.provider.service.porn.impl;

import com.easylive.mgs.provider.pub.model.TencentVoiceAO;
import com.easylive.mgs.provider.pub.model.TencentVoiceDTO;
import com.easylive.mgs.provider.service.porn.PornService;
import com.easylive.mgs.provider.service.porn.abstracts.AbstractVoicePorn;
import com.tencentcloudapi.asr.v20190614.models.SentenceRecognitionRequest;
import com.tencentcloudapi.asr.v20190614.models.SentenceRecognitionResponse;
import com.tencentcloudapi.common.Credential;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("VoicePornService")
public class VoicePornServiceImpl implements PornService<TencentVoiceDTO, TencentVoiceAO, Object> {


    @Value("${tencent.cloud.secretId}")
    private String secretId;

    @Value("${tencent.cloud.secretKey}")
    private String secretKey;

    /**
     * 引擎模型类型
     */
    private String VoiceEngSerViceType = "16k_zh";

    private Credential credential;

    @PostConstruct
    public void init() {
        credential = new Credential(secretId, secretKey);
    }

    /**
     * 语音内容解析
     *  设置 URL
     * @param tencentVoiceAO
     * @return
     */
    @Override
    public TencentVoiceDTO scanTask(TencentVoiceAO tencentVoiceAO) {

        AbstractVoicePorn voicePorn = new AbstractVoicePorn(credential, null);
        SentenceRecognitionRequest request = new SentenceRecognitionRequest();

        request.setEngSerViceType(VoiceEngSerViceType);

        //0：语音 URL；1：语音数据（post body)
        request.setSourceType(0L);

        //识别音频的音频格式
        request.setVoiceFormat(tencentVoiceAO.getFormat());

        //一句话识别
        request.setSubServiceType(2L);

        request.setUrl(tencentVoiceAO.getUrl());

        SentenceRecognitionResponse response = voicePorn.scanTask(request);

        return new TencentVoiceDTO(response.getResult());
    }

    @Override
    public String cancelTask(String taskId) {
        return null;
    }

    @Override
    public String describeTaskDetail(String taskId) {
        return null;
    }
}
