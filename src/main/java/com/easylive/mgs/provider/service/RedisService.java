package com.easylive.mgs.provider.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class RedisService {

    @Autowired
    private RedisTemplate redisTemplate;

//    /**
//     * 本方法在集群里面，不支持keys方法
//     * @param key
//     * @param incrementValue
//     * @param expirationMillis
//     * @return
//     */
//    @Deprecated
//    public Long incrementAndExpireDeprecated(String key, Long incrementValue, Long expirationMillis) {
//        String script = "local key = KEYS[1] " +
//                "local increment = tonumber(ARGV[1]) " +
//                "local expiration = tonumber(ARGV[2]) " +
//                "local current = tonumber(redis.call('GET', key) or 0) " +
//                "local new_value = current + increment " +
//                "redis.call('SET', key, new_value) " +
//                "redis.call('PEXPIRE', key, expiration) " +
//                "return new_value";
//
//        RedisScript<Long> redisScript = new DefaultRedisScript<>(script, Long.class);
//
//        List<String> keys = Collections.singletonList(key);
//        Object[] args = new Long[]{incrementValue, expirationMillis};
//
//        return (Long) redisTemplate.execute(redisScript, keys, args);
//    }
//
//    /**
//     *  ERR for redis cluster, eval/evalsha number of keys can't be negative or zero
//     * @param key
//     * @param incrementValue
//     * @param expirationMillis
//     * @return
//     */
//    @Deprecated
//    public Long incrementValAndExpire(String key, Long incrementValue, Long expirationMillis) {
//        //这里字符串替换，解析获包含双引号
//        String script = "local key = string.gsub(ARGV[1], '\"', '') " +
//                "local increment = tonumber(ARGV[2]) " +
//                "local expiration = tonumber(ARGV[3]) " +
//                "local current = tonumber(redis.call('GET', key) or 0) " +
//                "local new_value = current + increment " +
//                "redis.call('SET', key, new_value) " +
//                "redis.call('PEXPIRE', key, expiration) " +
//                "return new_value";
//
//        RedisScript<Long> redisScript = new DefaultRedisScript<>(script, Long.class);
//
//        Object[] args = new Object[]{key, incrementValue, expirationMillis};
//
//        return (Long) redisTemplate.execute(redisScript, Collections.emptyList(), args);
//    }


    public Long incrementAndExpire(String key, Long incrementValue, Long expirationMillis) {
        Long val = redisTemplate.opsForValue().increment(key, incrementValue);
        redisTemplate.expire(key, expirationMillis, TimeUnit.MILLISECONDS);
        return val;
    }
}
