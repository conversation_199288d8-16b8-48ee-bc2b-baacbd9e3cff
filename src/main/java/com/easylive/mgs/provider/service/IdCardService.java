package com.easylive.mgs.provider.service;

import com.aliyun.cloudauth20190307.Client;
import com.aliyun.cloudauth20190307.models.Id2MetaVerifyRequest;
import com.aliyun.cloudauth20190307.models.Id2MetaVerifyResponse;
import com.aliyun.cloudauth20190307.models.Id2MetaVerifyResponseBody;
import com.easylive.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.aliyun.teautil.models.RuntimeOptions;


@Service
@Slf4j
public class IdCardService {

    @Autowired
    @Qualifier("ID2MetaVerify")
    private Client iD2MetaVerifyClient;

    /**
     * 校验结果是否正确
     * @param idNo
     * @param name
     * @return
     */
    public boolean metaVerify(String idNo, String name) {
        // 通过以下代码创建API请求并设置参数。
        Id2MetaVerifyRequest request = new Id2MetaVerifyRequest();

        request.setParamType("normal");
        request.setUserName(name);
        request.setIdentifyNum(idNo);

        // 推荐，支持服务路由。
        Id2MetaVerifyResponseBody body = id2MetaVerifyAutoRoute(request);
        if (body == null) {
            return false;
        }

        return  "1".equals(body.getResultObject().getBizCode());
    }


    private Id2MetaVerifyResponseBody id2MetaVerifyAutoRoute(Id2MetaVerifyRequest request) {

        RuntimeOptions runtime = new RuntimeOptions();
        runtime.readTimeout = 5000;
        runtime.connectTimeout = 5000;

        try {
            Id2MetaVerifyResponse response = iD2MetaVerifyClient.id2MetaVerifyWithOptions(request, runtime);
            if (response == null) {
                return null;
            }

            log.info("Id2MetaVerify {} {},  response is {}", request.getUserName(), request.getIdentifyNum(), JsonUtil.toString(response.getBody()));

            if(500 == response.getStatusCode()){
                return null;
            }

            if (response.getBody() == null) {
                return null;
            }

            if(!"200".equals(response.getBody().getCode())){
                return null;
            }

            return response.getBody();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

}
