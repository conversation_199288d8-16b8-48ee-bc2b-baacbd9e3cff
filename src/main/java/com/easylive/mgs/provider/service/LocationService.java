package com.easylive.mgs.provider.service;


import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.geoip.model.v20200101.DescribeIpv4LocationRequest;
import com.aliyuncs.geoip.model.v20200101.DescribeIpv4LocationResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.easylive.common.util.HttpUtil;
import com.easylive.common.util.JsonUtil;
import com.easylive.mgs.provider.config.OssConfig;
import com.easylive.mgs.provider.entity.IpLocationEntity;
import com.easylive.mgs.provider.model.ali.AliIpLocation;
import com.easylive.mgs.provider.model.baidu.BaiduGpsRet;
import com.easylive.mgs.provider.model.ip138.IpOteRet;
import com.easylive.mgs.provider.model.taobao.IpLocationRet;
import com.easylive.mgs.provider.model.tencent.TencentIpLocation;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.model.location.GpsLocation;
import com.easylive.mgs.provider.pub.model.location.IpLocation;
import com.easylive.mgs.provider.repo.main.IpLocationRepository;
import com.easylive.mgs.provider.repo.read.IpLocationReadRepository;
import com.easylive.rpc.http.ResponseException;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/2 10:12 AM
 */

@Service
@Slf4j
public class LocationService {


    private static final String BAIDU_GPS_LOCATION_URL = "http://api.map.baidu.com/geocoder/v2/";
    private static final String TAOBAO_IP_LOCATION_URL = "http://ip.taobao.com/service/getIpInfo2.php?ip=";
    private static final String ALI_IP_LOCATION_URL = "http://iploc.market.alicloudapi.com/v3/ip";
    private static final String IP138_IP_LOCATION_URL = "https://api.ip138.com/ip/";
    private static final String TENCENT_LOCATION_URL = "https://apis.map.qq.com/ws/location/v1/ip";
    private static final String ALI_IP_LOCATION_APP_CODE = "c62bbfe50f42498883e0874f16cd71ba";
    private static final String IP138_IP_LOCATION_TOKEN = "722bc52b59e368352664902180e67bf4";
    private static final String TENCENT_LOCATION_KEY = "5ZSBZ-6MUKQ-DYH5D-BABMN-SBYL5-NOFYS";

    private static final String HUACHEN_IP_LOCATION_URL = "https://c2ba.api.huachen.cn/ip";
    private static final String HUACHEN_IP_LOCATION_APP_CODE = "7488e60394fd496e993f987e8268ed5c";

    private static final String CZ88_IP_LOCATION_URL = "https://cz88geoaliyun.cz88.net/search/ip/geo";
    private static final String CZ88_IP_LOCATION_APP_CODE = "7488e60394fd496e993f987e8268ed5c";

    private static final String FUSHU_IP_LOCATION_URL = "https://ipquery.market.alicloudapi.com/query";
    private static final String FUSHU_IP_LOCATION_APP_CODE = "7488e60394fd496e993f987e8268ed5c";

    @Autowired
    private IpLocationReadRepository ipLocationReadRepository;

    @Autowired
    private IpLocationRepository ipLocationRepository;

    @Autowired
    private OssConfig ossConfig;


    /**
     * AppKey：24965004
     * AppSecret：65c52f29ec2d7560e4f3661cf1f05d76
     * AppCode：c62bbfe50f42498883e0874f16cd71ba
     */


    public GpsLocation getLocationByGPS(Double longitude, Double latitude) {

        if (!isValidGps(longitude, latitude)) {
            //log.error("invalid gps longitude:{},latitude:{}", longitude, latitude);
            throw new ResponseException(ProviderError.E_GPS_PARAM);
        }

        Map<String, Object> params = Maps.newHashMap();
        params.put("ak", "jqmM1amAtN4CzjFHIE6GRCfI");
        params.put("output", "json");
        params.put("location", latitude.toString() + "," + longitude.toString());
        BaiduGpsRet baiduGpsRet = Optional.ofNullable(HttpUtil.get(BAIDU_GPS_LOCATION_URL, params, BaiduGpsRet.class)).orElseGet(BaiduGpsRet::new);
        if (baiduGpsRet.getStatus() != 0) {
            log.error("request baidu gps service error:{}", baiduGpsRet);
            throw new ResponseException(ProviderError.E_GPS_BAIDU);
        }
        GpsLocation gpsLocation = new GpsLocation();
        gpsLocation.setCountry(baiduGpsRet.getResult().getAddressComponent().getCountry());
        gpsLocation.setProvince(baiduGpsRet.getResult().getAddressComponent().getProvince());
        gpsLocation.setAddress(baiduGpsRet.getResult().getFormattedAddress());
        gpsLocation.setCity(baiduGpsRet.getResult().getAddressComponent().getCity());
        gpsLocation.setCode(baiduGpsRet.getResult().getCityCode());
        return gpsLocation;


    }

    public IpLocationInfo getIpLocation(String ip) {
        //IpLocationInfo info = getLocationFromFuShu(ip);
        //if (info == null)
            return getLocationFromHuaChen(ip);
        //return info;
    }

    public IpLocationInfo getLocationFromIp138(String ip) {

        log.info("get ip location from ip138 service {}", ip);
        HttpUtil.RequestOptions options = new HttpUtil.RequestOptions();
        options.setMethod(HttpUtil.RequestMethod.GET);
        options.setUrl(IP138_IP_LOCATION_URL);
        Map<String, String> headers = Maps.newHashMap();
        headers.put("token", IP138_IP_LOCATION_TOKEN);
        options.setHeaders(headers);
        Map<String, Object> params = Maps.newHashMap();
        params.put("ip", ip);
        options.setParams(params);
        Response response = HttpUtil.doRequestResponseReturn(options);
        if (response != null) {
            if (response.code() == HttpStatus.OK.value()) {
                log.info("ip138 return ok,ip:{}", ip);
                if (response.body() != null) {
                    try {
                        String bodyString = response.body().string();
                        log.info(bodyString);
                        IpOteRet ipOteRet = JsonUtil.fromString(bodyString, IpOteRet.class);
                        if (ipOteRet == null) {

                            return null;
                        }
                        IpLocationInfo ipLocationInfo = new IpLocationInfo();
                        List<String> returnData = ipOteRet.getData();
                        ipLocationInfo.setCountry(returnData.get(0));
                        ipLocationInfo.setProvince(returnData.get(1));
                        ipLocationInfo.setCity(returnData.get(2));
                        ipLocationInfo.setIsp(returnData.get(4));
                        return ipLocationInfo;
                    } catch (IOException e) {
                        log.error("ip138 io exception:", e);
                    }
                }
            } else if (response.code() == HttpStatus.ACCEPTED.value()) {
                log.info("ip138 return http status 202,ip:{}", ip);
                if (response.body() != null) {
                    try {
                        String bodyString = response.body().string();
                        if (!bodyString.contains("次数受限")) {
                            log.error(bodyString);
                        }
                    } catch (IOException e) {
                        log.error("ip138 io exception:", e);
                    }
                }

            } else {
                log.error("ip138 Unhandled error:{}", JsonUtil.toString(response));
            }

        }
        return null;

    }

    public IpLocationInfo getLocationFromTaoBao(String ip) {

        String ipServiceUrl = TAOBAO_IP_LOCATION_URL + ip;
        String response = HttpUtil.get(ipServiceUrl);
        if (response == null) {
            return null;
        }
        IpLocationRet ipLocationRet = Optional.ofNullable(JsonUtil.fromString(response, IpLocationRet.class)).orElseGet(IpLocationRet::new);
        if (ipLocationRet.getCode() != 0) {

            log.error("request taobao ip service error:{}", ipLocationRet);
            return null;
        }
        IpLocationInfo ipLocation = new IpLocationInfo();
        ipLocation.setCountry(ipLocationRet.getData().getCountry());
        ipLocation.setCity(ipLocationRet.getData().getCity());
        ipLocation.setProvince(ipLocationRet.getData().getRegion());
        ipLocation.setIsp(ipLocationRet.getData().getIsp());
        return ipLocation;
    }

    public IpLocationInfo getLocationFromAliForPay(String ip) {

        log.info("get ip location from ali pay service {}", ip);
        DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", ossConfig.getPornAccessKeyId(), ossConfig.getPornAccessKeySecret());
        IAcsClient client = new DefaultAcsClient(profile);

        DescribeIpv4LocationRequest request = new DescribeIpv4LocationRequest();
        request.setRegionId("cn-hangzhou");
        request.setIp(ip);

        try {
            DescribeIpv4LocationResponse response = client.getAcsResponse(request);
            IpLocationInfo ipLocationInfo = new IpLocationInfo();
            ipLocationInfo.setCity(response.getCity());
            ipLocationInfo.setCountry(response.getCountry());
            ipLocationInfo.setProvince(response.getProvince());
            ipLocationInfo.setIsp(response.getIsp());
            return ipLocationInfo;
        } catch (ServerException e) {
            log.error("ali ip server error:", e);
        } catch (ClientException e) {

            log.error("ali ip client error:", e);
        }
        return null;

    }

    /**
     * 腾讯ip解析内容
     *
     * @param ip
     * @return
     */
    public IpLocationInfo getLocationFromTencent(String ip) {

        log.info("get ip location from tencent service {}", ip);

        Map<String, Object> param = new HashMap<>();
        param.put("key", TENCENT_LOCATION_KEY);
        param.put("ip", ip);

        String response = "";
        try {
            response = HttpUtil.get(TENCENT_LOCATION_URL, param);
        } catch (Exception e) {
            log.error("Get location from tencent exception .", e);
            return null;
        }

        if (response.isEmpty()) {
            return null;
        }


        final TencentIpLocation tencentIpLocation = JsonUtil.fromString(response, TencentIpLocation.class);
        if (tencentIpLocation == null) {
            return null;
        }

        if (tencentIpLocation.getStatus() != 0 || !"Success".equals(tencentIpLocation.getMessage())) {
            log.error("Get location from tencent error. response:{}", response);
            return null;
        }

        final IpLocationInfo ipLocationInfo = new IpLocationInfo();
        TencentIpLocation.Result.AdInfo info = tencentIpLocation.getResult().getAdInfo();
        ipLocationInfo.setCountry(info.getNation());
        ipLocationInfo.setProvince(info.getProvince());
        ipLocationInfo.setCity(info.getCity());
        ipLocationInfo.setIsp("");
        return ipLocationInfo;
    }

    private IpLocationInfo getLocationFromHuaChen(String ip) {
        log.info("get ip location from huachen service {}", ip);
        Map<String, Object> params = new HashMap<>();
        params.put("ip", ip);

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + HUACHEN_IP_LOCATION_APP_CODE);


        HttpUtil.RequestOptions requestOptions = new HttpUtil.RequestOptions();
        requestOptions.setMethod(HttpUtil.RequestMethod.GET);
        requestOptions.setUrl(HUACHEN_IP_LOCATION_URL);
        requestOptions.setParams(params);
        requestOptions.setHeaders(headers);

        String response = "";
        try {
            response = HttpUtil.doRequest(requestOptions);
            HuaChenResponse huaChenResponse = JsonUtil.fromString(response, HuaChenResponse.class);
            if (huaChenResponse == null) {
                log.error("Invalid huanchen response:{}", response);
                return null;
            }

            if (huaChenResponse.ret != 200) {
                log.error("Get location from huachen error. response:{}", response);
                return null;
            }

            IpLocationInfo info = new IpLocationInfo();
            info.setCity(huaChenResponse.data.city);
            info.setCountry(huaChenResponse.data.country);
            info.setProvince(huaChenResponse.data.region);
            info.setIsp(huaChenResponse.data.isp);

            log.info("Get Location result: {}", info);
            return info;

        } catch (Exception e) {
            log.error("Error request huachen ip location ip:{}, {}, {}", ip, e.getMessage(), response);
            return null;
        }
    }


    private IpLocationInfo getLocationFromCZ88(String ip) {
        log.info("get ip location from CZ88 service {}", ip);
        Map<String, Object> params = new HashMap<>();
        params.put("ip", ip);

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + CZ88_IP_LOCATION_APP_CODE);


        HttpUtil.RequestOptions requestOptions = new HttpUtil.RequestOptions();
        requestOptions.setMethod(HttpUtil.RequestMethod.GET);
        requestOptions.setUrl(CZ88_IP_LOCATION_URL);
        requestOptions.setParams(params);
        requestOptions.setHeaders(headers);

        String response = "";
        try {
            //如果欠费：HTTP请求结果为403，Response Header包括: X-Ca-Error-Message: Api Market Subscription quota exhausted

            response = HttpUtil.doRequest(requestOptions);
            CZ88Response cz88Response = JsonUtil.fromString(response, CZ88Response.class);
            if (cz88Response == null) {
                log.error("Invalid CZ88 response:{}", response);
                return null;
            }

            if (cz88Response.code != 200) {
                log.error("Get location from CZ88 error. response:{}", response);
                return null;
            }

            IpLocationInfo info = new IpLocationInfo();
            info.setCity(cz88Response.data.city);
            info.setCountry(cz88Response.data.country);
            info.setProvince(cz88Response.data.province);
            info.setIsp(cz88Response.data.isp);

            log.info("Get Location result from CZ88: {}", info);
            return info;

        } catch (Exception e) {
            log.error("Error request CZ88 ip location ip:{}, {}, {}", ip, e.getMessage(), response);
            return null;
        }
    }


    private IpLocationInfo getLocationFromFuShu(String ip) {
        log.info("get ip location from Fushu service {}", ip);
        Map<String, Object> params = new HashMap<>();
        params.put("ip", ip);

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + FUSHU_IP_LOCATION_APP_CODE);


        HttpUtil.RequestOptions requestOptions = new HttpUtil.RequestOptions();
        requestOptions.setMethod(HttpUtil.RequestMethod.GET);
        requestOptions.setUrl(FUSHU_IP_LOCATION_URL);
        requestOptions.setParams(params);
        requestOptions.setHeaders(headers);

        String response = "";
        try {
            response = HttpUtil.doRequest(requestOptions);
            FushuResponse fuShuResponse = JsonUtil.fromString(response, FushuResponse.class);
            if (fuShuResponse == null) {
                log.error("Invalid Fushu response:{}", response);
                return null;
            }

            if (fuShuResponse.ret != 200) {
                log.error("Get location from Fushu error. response:{}", response);
                return null;
            }

            // HTTP错误码403，会抛出异常，走不到这里,同CZ88
            // {protocol=h2, code=403, message=, url=https://ipquery.market.alicloudapi.com/query?ip=************}

            IpLocationInfo info = new IpLocationInfo();
            info.setCity(fuShuResponse.data.city);
            info.setCountry(fuShuResponse.data.country);
            info.setProvince(fuShuResponse.data.province);
            info.setIsp(fuShuResponse.data.isp);

            log.info("Get Location result from Fushu: {}", info);
            return info;

        } catch (Exception e) {
            log.error("Error request Fushu ip location ip:{}, {}, {}", ip, e.getMessage(), response);
            return null;
        }
    }

    public IpLocationInfo getLocationFromAliForFree(String ip) {

        log.info("get ip location from ali free service {}", ip);

        Map<String, Object> params = new HashMap<>();
        params.put("ip", ip);
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + ALI_IP_LOCATION_APP_CODE);


        HttpUtil.RequestOptions requestOptions = new HttpUtil.RequestOptions();
        requestOptions.setMethod(HttpUtil.RequestMethod.GET);
        requestOptions.setUrl(ALI_IP_LOCATION_URL);
        requestOptions.setParams(params);
        requestOptions.setHeaders(headers);

        String response = "";
        try {
            response = HttpUtil.doRequest(requestOptions);
        } catch (Exception e) {
            log.error("Request ali ip location ip:{}, {}", ip, e.getMessage());
            return null;
        }


        //ali 错误格式(无效json) {"status":"1","info":"OK","infocode":"10000","province":[],"city":[],"adcode":[],"rectangle":[]}
        if (response.contains("\"province\":[],\"city\":[]") || response.contains("Forbidden")) {
            log.info("Ali ip location ip:{} response:{}", ip, response);
            return null;
        }

        AliIpLocation aliIpLocation = Optional.ofNullable(JsonUtil.fromString(response, AliIpLocation.class)).orElseGet(AliIpLocation::new);

        if (!"OK".equals(aliIpLocation.getInfo())) {

            log.info("[ERROR]get ip location from ali error:{}", aliIpLocation);
            return null;
        }


        IpLocationInfo ipLocation = new IpLocationInfo();
        ipLocation.setCountry("中国");
        ipLocation.setCity(aliIpLocation.getCity());
        ipLocation.setProvince(aliIpLocation.getProvince());
        ipLocation.setIsp("");
        return ipLocation;


    }


    public IpLocation getLocationByIp(String ip) {

        return ipLocationReadRepository.findByIp(ip).map((v) -> {
            // 更新时间大于一个月应该触发更新
            if ((System.currentTimeMillis() - v.getUpdateTime().getTime()) > 6 * 86400 * 30 * 1000L) {

                Optional.ofNullable(getIpLocation(ip)).ifPresent((ipInfo) -> {
                    v.setCity(ipInfo.getCity());
                    v.setCountry(ipInfo.getCountry());
                    v.setIsp(ipInfo.getIsp());
                    v.setProvince(ipInfo.getProvince());
                    ipLocationRepository.save(v);
                });
            }
            IpLocation ipLocation = new IpLocation();
            ipLocation.setCity(v.getCity());
            ipLocation.setCountry(v.getCountry());
            ipLocation.setIp(v.getIp());
            ipLocation.setIsp(v.getIsp());
            ipLocation.setProvince(v.getProvince());
            return ipLocation;
        }).orElseGet(() -> Optional.ofNullable(getIpLocation(ip)).map((v) -> {

                    IpLocationEntity saveEntity = new IpLocationEntity();
                    saveEntity.setCity(v.getCity());
                    saveEntity.setIp(ip);
                    saveEntity.setCountry(v.getCountry());
                    saveEntity.setIsp(v.getIsp());
                    saveEntity.setProvince(v.getProvince());
                    try {
                        ipLocationRepository.save(saveEntity);
                    } catch (Exception e) {
                    }
                    IpLocation ipLocation = new IpLocation();
                    ipLocation.setCity(v.getCity());
                    ipLocation.setIp(ip);
                    ipLocation.setIsp(v.getIsp());
                    ipLocation.setCountry(v.getCountry());
                    ipLocation.setProvince(v.getProvince());
                    return ipLocation;
                }
        ).orElseThrow(() -> new ResponseException(ProviderError.E_IP)));

    }

    private boolean isValidGps(Double longitude, Double latitude) {

        if (latitude == null || longitude == null) {
            return false;
        }

        if (latitude == 0 || longitude == 0) {
            return false;
        }

        if (!((latitude <= 90 && latitude >= -90) && (longitude >= -180 && longitude <= 180))) {

            return false;
        }

        return true;

    }

    @Data
    public static class IpLocationInfo {
        private String country;
        private String province;
        private String isp;
        private String city;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class HuaChenResponse {
        private int ret;
        private String msg;
        private HuaChenData data;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class HuaChenData {
        private String ip;
        private String area;
        private String region;
        private String city;
        private String isp;
        private String country;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class CZ88Response {
        private int code;
        private boolean success;
        private String message;
        private CZ88Data data;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class CZ88Data {
        private String ip;
        private String country;
        private String countryCode;
        private String province;
        private String city;
        private String districts;
        private String isp;
        private String iana;
        private String continent;
        private String ianaEn;
    }


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class FushuResponse {
        private int ret;
        private FushuData data;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class FushuData {
        private String ip;
        private String country;

        @JsonProperty("country_code")
        private String countryCode;

        @JsonProperty("prov")
        private String province;
        private String city;
        private String area;
        private String isp;
    }


    public static void main(String[] args) {
        LocationService locationService = new LocationService();
        String ip = "**************";
        IpLocationInfo info = locationService.getLocationFromHuaChen(ip);
        log.info("{}", info);
        info = locationService.getLocationFromCZ88(ip);
        log.info("{}", info);
        info = locationService.getLocationFromFuShu(ip);
        log.info("{}", info);
    }
}
