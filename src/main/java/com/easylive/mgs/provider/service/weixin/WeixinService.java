package com.easylive.mgs.provider.service.weixin;

import com.easylive.common.util.HttpUtil;
import com.easylive.common.util.JsonUtil;
import com.easylive.mgs.provider.entity.WeixinAppEntity;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.model.WeiXinUserPhoneDTO;
import com.easylive.mgs.provider.pub.model.WeixinJscode2SessionDTO;
import com.easylive.rpc.http.ResponseException;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Service
@Slf4j
public class WeixinService {
    private static final String WEIXIN_APP_GENERATE_SCHEME_URL = "https://api.weixin.qq.com/wxa/generatescheme?access_token=%s";

    private static final int WEIXIN_APPLET_MAX_EXPIRE_TIME_IN_MINUTES = 30 * 24 * 60;
    private static final int WEIXIN_APPLET_MIN_EXPIRE_TIME_IN_MINUTES = 1;

    private static final int WEIXIN_ERR_INVALID_ACCESS_TOKEN = 40001;
    private static final int WEIXIN_SERVICE_MAX_RETRY_TIMES = 3;

    @Autowired
    private WeixinAccessTokenService weixinAccessTokenService;

    @PostConstruct
    public void init() {
        log.info("WeixinService init");
    }

    public void reload() {
        weixinAccessTokenService.getWeixinApps().forEach(weixinApp -> {
            weixinAccessTokenService.refreshAccessToken(weixinApp.getAppId(), false);
        });
    }

    public String generateScheme(String appId, String path, String query, String env, int validTime) {
        log.info("Generate weixin mini program scheme:{} , {}, {}, {}, {}", appId, path, query, env, validTime);

        if( validTime < WEIXIN_APPLET_MIN_EXPIRE_TIME_IN_MINUTES || validTime > WEIXIN_APPLET_MAX_EXPIRE_TIME_IN_MINUTES)
            throw new ResponseException(ProviderError.E_WEIXIN_APPLET_INVALID_EXPIRE_TIME, "Valid time should be 1 - 43200 minutes");


        int retry = 0;
        while (retry < WEIXIN_SERVICE_MAX_RETRY_TIMES) {
            retry++;
            if( retry > 1 ) {
                log.info("Retry {} times", retry);
            }
            String accessToken = weixinAccessTokenService.getAccessToken(appId);
            try {
                return internalGenerateScheme(accessToken, path, query, env, validTime);
            } catch (ResponseException e) {
                if (e.getError().getCode() == ProviderError.E_WEIXIN_ACCESS_TOKEN_INVALID.getCode()) {
                    weixinAccessTokenService.refreshAccessToken(appId, true);
                    continue;
                }
            }
            break;
        }
        log.error("Error occurs when generate scheme");
        return null;
    }

    public WeixinJscode2SessionDTO jscode2Session(String appId, String jsCode) {
        return weixinAccessTokenService.jsCode2Session(appId, jsCode);
    }

    public WeiXinUserPhoneDTO getPhoneNumber(String appId, String code){
        return weixinAccessTokenService.getPhoneNumber(appId, code);
    }

    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    private static class GenerateSchemeRequest {
        @Data
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class JumpParam {
            private String path;
            private String query;
            private String envVersion;
        }

        private JumpParam jumpWxa;
        private boolean isExpire;
        private int expireType;
        private int expireInterval;
    }

    @Data
    private static class GenerateSchemeResult {
        private int errcode;
        private String errmsg;
        private String openlink;
    }

    private String internalGenerateScheme(String accessToken, String path, String query, String env, int validTime) {

        Map<String, Object> jumpWxa = new HashMap<>();
        if (!StringUtils.isEmpty(path))
            jumpWxa.put("path", path);
        if (!StringUtils.isEmpty(query))
            jumpWxa.put("query", query);
        if (!StringUtils.isEmpty(env))
            jumpWxa.put("env_version", env);

        Map<String, Object> params = new HashMap<>();
        params.put("jump_wxa", jumpWxa);
        params.put("is_expire", true);
        params.put("expire_type", 1);
        params.put("expire_interval", validTime);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        HttpUtil.RequestOptions options = new HttpUtil.RequestOptions();
        options.setMethod(HttpUtil.RequestMethod.POST);
        options.setHeaders(headers);
        options.setParams(params);

        log.info("Generating weixin applet scheme: {}", params);
        String url = String.format(WEIXIN_APP_GENERATE_SCHEME_URL, accessToken);
        options.setUrl(url);
        GenerateSchemeResult result = HttpUtil.doRequest(options, GenerateSchemeResult.class);
        if (result == null) {
            log.error("Error occurs  when generate scheme");
            return null;
        }

        log.info("Result:{}", JsonUtil.toString(result));

        if (result.errcode == WEIXIN_ERR_INVALID_ACCESS_TOKEN) {
            log.warn("Invalid access token used");
            throw new ResponseException(ProviderError.E_WEIXIN_ACCESS_TOKEN_INVALID, "Invalid access token");
        }

        if (result.errcode != 0) {
            throw new ResponseException(ProviderError.E_WEIXIN, "Error occurs when generate scheme");
        }

        return result.openlink;
    }

}
