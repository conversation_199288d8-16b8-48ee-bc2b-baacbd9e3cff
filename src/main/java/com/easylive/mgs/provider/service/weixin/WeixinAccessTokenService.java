package com.easylive.mgs.provider.service.weixin;

import cn.hutool.core.convert.ConvertException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyuncs.utils.StringUtils;
import com.easylive.common.util.DateTimeUtil;
import com.easylive.common.util.HttpUtil;
import com.easylive.common.util.JsonUtil;
import com.easylive.mgs.provider.entity.WeixinAppEntity;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.model.WeiXinUserPhoneDTO;
import com.easylive.mgs.provider.pub.model.WeixinJscode2SessionDTO;
import com.easylive.mgs.provider.repo.provider.WeixinAppRepository;
import com.easylive.rpc.http.ResponseException;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class WeixinAccessTokenService {
    private static final String WEIXIN_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";
    private static final String WEIXIN_APP_JSCODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session";
    private static final String WEIXIN_USER_PHONE_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=";

    private static final int WEIXIN_ACCESS_TOKEN_MIN_LEN = 64;

    @Autowired
    private WeixinAppRepository weixinAppRepository;


    public List<WeixinAppEntity> getWeixinApps() {
        return weixinAppRepository.findAll();
    }


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    static class Jscode2SessionResult {
        private int errcode;
        private String errmsg;
        private String openid;
        private String session_key;
        private String unionid;
    }

    public WeiXinUserPhoneDTO getPhoneNumber(String appId, String code){
        String accessToken = getAccessToken(appId);
        Map<String,Object> params = new HashMap<>(1);
        params.put("code", code);
        String result = cn.hutool.http.HttpUtil.post(WEIXIN_USER_PHONE_URL + accessToken, JsonUtil.toString(params));
        log.info("[getPhoneNumber] result: {}", result);

        WeiXinUserPhoneDTO userPhoneDTO;
        try {
            JSONObject resultJ = JSONUtil.parseObj(result);
            Integer errCode = resultJ.getInt("errcode");
            userPhoneDTO = new WeiXinUserPhoneDTO(errCode, resultJ.getStr("errmsg"));
            if (errCode == 0){
                JSONObject phoneInfo = resultJ.get("phone_info", JSONObject.class);
                userPhoneDTO.setWeiXinUserPhone(phoneInfo.getStr("phoneNumber"), phoneInfo.getStr("purePhoneNumber"), phoneInfo.getInt("countryCode"));
            }
        } catch (Exception e) {
            log.error("[getPhoneNumber] parse result error, appId:{}, code:{}, result:{}", appId, code, result, e);
            throw new ResponseException(ProviderError.E_WEIXIN_SERVICE);
        }
        return userPhoneDTO;
    }


    public WeixinJscode2SessionDTO jsCode2Session(String appId, String code) {
        String secret = getSecretKey(appId);
        Map<String,Object> params = new HashMap<>();
        params.put("appid", appId);
        params.put("secret",secret);
        params.put("js_code", code);
        params.put("grant_type", "authorization_code");
        log.info("[jscode2session] request params:{}",JsonUtil.toString(params));
        String result = HttpUtil.get(WEIXIN_APP_JSCODE2SESSION_URL, params);
        log.info("[jscode2session] result: {}", result);

        Jscode2SessionResult resultObj = JsonUtil.fromString(result, Jscode2SessionResult.class);
        if( resultObj == null) {
            throw new ResponseException(ProviderError.E_WEIXIN_SERVICE);
        }

        WeixinJscode2SessionDTO dto = new WeixinJscode2SessionDTO();
        dto.setCode(resultObj.getErrcode());
        dto.setMessage(resultObj.getErrmsg());
        if (resultObj.getErrcode() == 0) {
            dto.setOpenId(resultObj.getOpenid());
            dto.setSessionKey(resultObj.getSession_key());
            dto.setUnionId(resultObj.getUnionid());
        }

        return dto;
    }

    public String getAccessToken(String appId) {
        WeixinAppEntity weixinApp = weixinAppRepository.findByAppId(appId);
        if (weixinApp == null)
            throw new ResponseException(ProviderError.E_WEIXIN_APP_NOT_EXISTS, "App not found");
        return weixinApp.getAccessToken();
    }

    private String getSecretKey(String appId) {
        WeixinAppEntity weixinApp = weixinAppRepository.findByAppId(appId);
        if (weixinApp == null)
            throw new ResponseException(ProviderError.E_WEIXIN_APP_NOT_EXISTS, "App not found");
        return weixinApp.getSecretKey();
    }
    private boolean isValidAccessToken(String token) {
        return org.apache.commons.lang3.StringUtils.length(token) > WEIXIN_ACCESS_TOKEN_MIN_LEN;
    }


    @Transactional(transactionManager = "providerTransactionManager")
    public void refreshAccessToken(String appId, boolean force) {
        log.info("Refresh access token for {}, {}", appId, force);
        WeixinAppEntity weixinApp = weixinAppRepository.findByAppIdWithLock(appId);
        if (weixinApp == null)
            throw new ResponseException(ProviderError.E_WEIXIN_APP_NOT_EXISTS, "App not found");

        if( ! force ) {
            Date expireTime = weixinApp.getExpireTime();
            Date now = DateTimeUtil.now();
            if( isValidAccessToken(weixinApp.getAccessToken()) && ( expireTime != null && expireTime.after(now)) ) {
                return;
            }
        }

        AccessToken token = internalGetAccessToken(weixinApp.getAppId(), weixinApp.getSecretKey());
        if (token == null ) {
            return;
        }

        int seconds = token.expiresIn - 600;
        if( seconds <= 600 )
            seconds = token.expiresIn;

        DateTime dateTime = new DateTime().plusSeconds(seconds);
        weixinApp.setAccessToken(token.accessToken);
        weixinApp.setExpireTime(dateTime.toDate());
        weixinAppRepository.save(weixinApp);
    }


    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class AccessToken {
        private String accessToken;
        private int expiresIn;
    }

    private AccessToken internalGetAccessToken(String appId, String secretKey) {
        Map<String,Object> params = new HashMap<>();
        params.put("appid",appId);
        params.put("secret",secretKey);
        params.put("grant_type","client_credential");
        String result = HttpUtil.get(WEIXIN_ACCESS_TOKEN_URL,params);
        log.info("Get access token: {}", result);
        if (result == null || result.isEmpty()) {
            log.error("Error occurs ");
            return null;
        }
        AccessToken token = JsonUtil.fromString(result, AccessToken.class);
        if( token == null || StringUtils.isEmpty(token.accessToken)) {
            log.error("error generate access token");
            return null;
        }
        log.info("Access token refreshed: {},{}", appId, token);
        return token;
    }
}
