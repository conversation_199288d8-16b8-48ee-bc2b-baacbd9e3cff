package com.easylive.mgs.provider.service.cdn;

import com.easylive.common.util.DateTimeUtil;
import com.easylive.common.util.JsonUtil;
import com.easylive.mgs.provider.enums.CDNRefreshStatus;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.enums.CDNRefreshType;
import com.easylive.rpc.http.ResponseException;
import com.tencentcloudapi.cdn.v20180606.CdnClient;
import com.tencentcloudapi.cdn.v20180606.models.*;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;

@Slf4j
public class TencentCDNProvider implements CDNProvider {
    private final CdnClient client;

    public TencentCDNProvider(String accessKeyId, String accessKeySecret) {
        Credential cred = new Credential(accessKeyId, accessKeySecret);
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("cdn.tencentcloudapi.com");
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        this.client = new CdnClient(cred, "", clientProfile);
    }

    @Override
    public String refresh(String[] urls, CDNRefreshType type, boolean force) {
        try {
            if (type == CDNRefreshType.FILE) {
                PurgeUrlsCacheRequest request = new PurgeUrlsCacheRequest();
                request.setUrls(urls);
                PurgeUrlsCacheResponse response = client.PurgeUrlsCache(request);
                log.info("CDN refresh file result: {}", response.getTaskId());
                return response.getTaskId();
            } else if (type == CDNRefreshType.DIRECTORY) {
                PurgePathCacheRequest request = new PurgePathCacheRequest();
                request.setPaths(urls);
                request.setFlushType("flush");
                PurgePathCacheResponse response = client.PurgePathCache(request);
                log.info("CDN refresh directory result: {}", response.getTaskId());
                return response.getTaskId();
            }
        } catch (Exception e) {
            log.error("CDN refresh error: {}", e.getMessage());
            throw new ResponseException(ProviderError.E_CDN_REFRESH);
        }

        return "";
    }

    @Override
    public String preload(String[] urls) {
        return "";
    }

    @Override
    public CDNRefreshTask checkStatus(String domain, String taskId) throws Exception {
        DescribePurgeTasksRequest describePurgeRequest = new DescribePurgeTasksRequest();
        describePurgeRequest.setTaskId(taskId);
        DescribePurgeTasksResponse describePurgeResponse = client.DescribePurgeTasks(describePurgeRequest);
        if( describePurgeResponse == null || describePurgeResponse.getPurgeLogs() == null || describePurgeResponse.getPurgeLogs().length == 0 )
            return null;
        PurgeTask purgeTask = describePurgeResponse.getPurgeLogs()[0];
        CDNRefreshTask task = new CDNRefreshTask();
        task.setTaskId(purgeTask.getTaskId());
        URL url = new URL(purgeTask.getUrl());
        task.setDomain(url.getHost());
        task.setPath(url.getPath());
        task.setCreateTime(DateTimeUtil.dateTimeFormatter().parseDateTime(purgeTask.getCreateTime()).toDate());
        task.setStatus(mapStatus(purgeTask.getStatus()));
        task.setType( mapType(purgeTask.getPurgeType()));
        task.setProcess("");
        return task;
    }

    private CDNRefreshStatus mapStatus(String status) {
        switch (status) {
            case "process":
                return CDNRefreshStatus.REFRESHING;
            case "done":
                return CDNRefreshStatus.COMPLETE;
            case "fail":
                return CDNRefreshStatus.FAILED;
            default:
                return CDNRefreshStatus.UNKNOWN;
        }
    }
    private CDNRefreshType mapType(String type) {
        switch (type) {
            case "url":
                return CDNRefreshType.FILE;
            case "dir":
                return CDNRefreshType.DIRECTORY;
            default:
                return null;
        }
    }
}
