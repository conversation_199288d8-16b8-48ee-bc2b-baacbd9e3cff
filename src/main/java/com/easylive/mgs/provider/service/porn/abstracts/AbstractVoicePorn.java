package com.easylive.mgs.provider.service.porn.abstracts;

import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.rpc.http.ResponseException;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.tencentcloudapi.asr.v20190614.models.SentenceRecognitionRequest;
import com.tencentcloudapi.asr.v20190614.models.SentenceRecognitionResponse;
import com.tencentcloudapi.common.AbstractClient;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.JsonResponseModel;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;


/**
 * <AUTHOR>
 */
@Slf4j
public class AbstractVoicePorn extends AbstractClient {

    private static String endpoint = "asr.tencentcloudapi.com";
    private static String version = "2019-06-14";


    public AbstractVoicePorn(Credential credential, String region) {
        this(credential, region, new ClientProfile());
    }

    public AbstractVoicePorn(Credential credential, String region, ClientProfile profile) {
        super(AbstractVoicePorn.endpoint, AbstractVoicePorn.version, credential, region, profile);
    }


    /**
     * 本接口用于对60秒之内的短音频文件进行识别。
     * •   支持中文普通话、英语、粤语、日语、越南语、马来语、印度尼西亚语、菲律宾语、泰语、上海话、四川话、武汉话、贵阳话、昆明话、西安话、郑州话、
     *     太原话、兰州话、银川话、西宁话、南京话、合肥话、南昌话、长沙话、苏州话、杭州话、济南话、天津话、石家庄话、黑龙江话、吉林话、辽宁话。
     * •   支持本地语音文件上传和语音URL上传两种请求方式，音频时长不能超过60s，音频文件大小不能超过3MB。<br>•   音频格式支持wav、pcm、ogg-opus、speex、silk、mp3、m4a、aac
     * •   请求方法为 HTTP POST , Content-Type为"application/json; charset=utf-8"
     * •   签名方法参考 [公共参数](https://cloud.tencent.com/document/api/1093/35640) 中签名方法v3。
     * •   默认接口请求频率限制：30次/秒，如您有提高请求频率限制的需求，请[前往购买](https://buy.cloud.tencent.com/asr)。
     * @param req SentenceRecognitionRequest
     * @return SentenceRecognitionResponse
     * @throws ResponseException
     */
    public SentenceRecognitionResponse scanTask(SentenceRecognitionRequest req) {
        JsonResponseModel<SentenceRecognitionResponse> rsp = null;
        String rspStr = "";
        try {
            Type type = new TypeToken<JsonResponseModel<SentenceRecognitionResponse>>() {
            }.getType();
            rspStr = this.internalRequest(req, "SentenceRecognition");
            rsp  = gson.fromJson(rspStr, type);
        } catch (JsonSyntaxException | TencentCloudSDKException e) {

            log.error("Create tencent sentence task fail. ", e);
            throw new ResponseException(ProviderError.E_TENCENT_CREATE);
        }

        return rsp.response;
    }
}
