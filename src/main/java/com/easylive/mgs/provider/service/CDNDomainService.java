package com.easylive.mgs.provider.service;

import com.easylive.common.util.DateTimeUtil;
import com.easylive.mgs.provider.common.Constants;
import com.easylive.mgs.provider.entity.CDNDomainEntity;
import com.easylive.mgs.provider.entity.CDNDomainRefreshTaskEntity;
import com.easylive.mgs.provider.entity.CdnDomainSchedEntity;
import com.easylive.mgs.provider.entity.CdnDomainSchedItemEntity;
import com.easylive.mgs.provider.enums.CDNRefreshStatus;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.cdn.DomainSignInfo;
import com.easylive.mgs.provider.pub.cdn.URLSignerFactory;
import com.easylive.mgs.provider.pub.enums.CDNDomainType;
import com.easylive.mgs.provider.pub.enums.CDNRefreshType;
import com.easylive.mgs.provider.pub.enums.CDNSignType;
import com.easylive.mgs.provider.pub.enums.CloudProviderType;
import com.easylive.mgs.provider.pub.model.CDNDomainDTO;
import com.easylive.mgs.provider.pub.model.CDNDomainSchedDTO;
import com.easylive.mgs.provider.pub.model.CloudProviderAccessDTO;
import com.easylive.mgs.provider.pub.util.TimeUtil;
import com.easylive.mgs.provider.pub.util.UrlUtil;
import com.easylive.mgs.provider.repo.provider.CDNDomainRefreshTaskRepository;
import com.easylive.mgs.provider.repo.provider.CDNDomainRepository;
import com.easylive.mgs.provider.repo.provider.CDNDomainSchedRepository;
import com.easylive.mgs.provider.service.cdn.*;
import com.easylive.rpc.http.ResponseException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 提供CDN相关服务，包括刷新路径，签名等
 * 一个CDN域名属于某一个服务商，并且关联服务商下的某个账号(用于对应AK-SK)
 *
 * <AUTHOR>
 * @date 2024/6/25
 */

@Slf4j
@Service
public class CDNDomainService {
    @Autowired
    private CDNDomainRepository cdnDomainRepository;

    @Autowired
    private CDNDomainRefreshTaskRepository cdnDomainRefreshTaskRepository;

    @Autowired
    private CDNDomainSchedRepository cdnDomainSchedRepository;

    @Autowired
    private CloudProviderService cloudProviderService;

    private volatile Map<String, CDNDomainEntity> domainMap = new HashMap<>();
    private volatile Map<Long, CDNDomainEntity> domainIdMap = new HashMap<>();

    private final Map<Long, CDNProvider> cdnProviders = new ConcurrentHashMap<>();

    private volatile Map<String, CDNDomainSchedDTO> domainSchedMap = new HashMap<>();
    @PostConstruct
    public void init() {
        load();
    }

    public List<CDNDomainDTO> listAll() {
        return domainMap.values().stream().map(this::domainEntityToDTO).collect(Collectors.toList());
    }

    public void loadSched() {
        HashMap<String, CDNDomainSchedDTO> tmpMap = new HashMap<>();
        for (CdnDomainSchedEntity entity : cdnDomainSchedRepository.findAll()) {
            if(!entity.getEnabled())
                continue;

            CDNDomainSchedDTO dto = new CDNDomainSchedDTO();
            dto.setName(entity.getName());
            dto.setDescription(entity.getDescription());

            String validTime = entity.getValidTime();
            List<CDNDomainSchedDTO.TimeRange>  validTimeList = parseValidTime(validTime);
            if (!validTimeList.isEmpty()) {
                dto.setValidTime(validTimeList);
            } else {
                log.warn("Invalid valid time for {} :{}", entity.getName(), validTime);
            }

            if( entity.getItems() != null ) {
                for (CdnDomainSchedItemEntity item : entity.getItems()) {
                    CDNDomainSchedDTO.CDNDomainSchedItemDTO itemDTO = new CDNDomainSchedDTO.CDNDomainSchedItemDTO();
                    CDNDomainEntity domain = domainIdMap.get(item.getDomainId());
                    if( domain != null ) {
                        itemDTO.setDomain( domain.getName());
                        itemDTO.setWeight(item.getWeight());
                        dto.getItems().add(itemDTO);
                    } else {
                        log.warn("CDN Domain not found, item:{}", item.getDomainId());
                    }
                }
            }
            tmpMap.put(entity.getName(), dto);
        }
        domainSchedMap = tmpMap;
    }

    private List<CDNDomainSchedDTO.TimeRange> parseValidTime(String validTime) {
        List<CDNDomainSchedDTO.TimeRange> ret = new ArrayList<>();
        for (Pair<LocalTime,LocalTime> item: TimeUtil.parseTimeRange(validTime)) {
            CDNDomainSchedDTO.TimeRange range = new CDNDomainSchedDTO.TimeRange(item.getLeft(), item.getRight());
            ret.add(range);
        }
        return ret;
    }


    public void load() {
        Map<String, CDNDomainEntity> tmpMap = new HashMap<>();
        Map<Long, CDNDomainEntity> tmpIdMap = new HashMap<>();
        List<CDNDomainEntity> list = cdnDomainRepository.findAll();
        list.forEach(entity -> {
            CloudProviderAccessDTO access = cloudProviderService.getAccessById(entity.getProviderAccessId());
            if (access != null) {
                entity.setProviderName(access.getName());
                CloudProviderType type = CloudProviderType.fromName(access.getType());
                if (type != null) {
                    entity.setProviderType(type);
                    entity.setProviderAccessKey(access.getAccessKey());
                    entity.setProviderSecretKey(access.getSecretKey());
                    entity.setRegion(access.getRegion());
                    tmpMap.put(entity.getName(), entity);
                    tmpIdMap.put(entity.getId(), entity);
                    log.info("Add CDN Domain:{},{}", entity.getName(), entity.getProviderType().getName());
                }
            }
        });

        log.info("Loaded CDN Domain:{}", tmpMap.size());
        domainMap = tmpMap;
        domainIdMap = tmpIdMap;
    }

    public CDNDomainDTO getDomain(String domain) {
        return domainEntityToDTO(domainMap.get(domain));
    }

    private CDNDomainEntity getDomainEntity(String domain) {
        return domainMap.get(domain);
    }

    private CDNDomainDTO domainEntityToDTO(CDNDomainEntity entity) {
        if (entity == null) {
            return null;
        }
        CDNDomainDTO dto = new CDNDomainDTO();
        dto.setId(Math.toIntExact(entity.getId()));
        dto.setName(entity.getName());
        dto.setProviderType(entity.getProviderType().getName());
        dto.setSignKey(entity.getMainSignKey());
        dto.setSignType(entity.getSignType());
        dto.setType(entity.getType());
        dto.setProviderAccessId(entity.getProviderAccessId());
        return dto;
    }

    public String refresh(String url, CDNRefreshType type, boolean force) {
        try {
            URL u = new URL(url);
            return refresh(u.getHost(), Collections.singletonList(u.getPath()), type, force);
        } catch (MalformedURLException e) {
            throw new ResponseException(ProviderError.E_CDN_REFRESH, "Invalid URL");
        }
    }

    public String refresh(String domain, List<String> paths, CDNRefreshType type, boolean force) {
        log.info("Refresh CDN: {}, {}, {}, {}", domain, paths, type, force);
        CDNDomainEntity entity = ensureExists(domain);
        if( entity.getType() != CDNDomainType.CDN.getValue() ) {
            throw new ResponseException(ProviderError.E_CDN_REFRESH, "Not a CDN domain");
        }


        CDNProvider provider = getProvider(entity);
        try {
            // concat domain to paths
            List<String> urls = new ArrayList<>();
            paths.forEach(path -> urls.add(UrlUtil.buildWithUrlEncode(Constants.HTTP_SCHEME, domain, path)));
            String refreshTaskId = provider.refresh(urls.toArray(new String[0]), type, force);
            //save task
            CDNDomainRefreshTaskEntity task = new CDNDomainRefreshTaskEntity();
            task.setDomain(domain);
            task.setProvider(entity.getProviderType().getName());
            task.setPath(StringUtils.join(paths, ","));
            task.setProcess("");
            task.setStatus(CDNRefreshStatus.COMMITTED.getValue());
            task.setType(type.getName());
            task.setTaskId(refreshTaskId);
            task.setCommitTime(new Date());
            cdnDomainRefreshTaskRepository.save(task);

            log.info("Commit CDN Refresh Task OK: {}", refreshTaskId);
            return refreshTaskId;
        } catch (Exception e) {
            log.error("Refresh CDN failed: {}", e.getMessage());
            throw new ResponseException(ProviderError.E_CDN, "Refresh CDN Error");
        }
    }

    private synchronized CDNProvider getProvider(CDNDomainEntity entity) {
        CDNProvider provider = cdnProviders.get(entity.getId());
        if (provider == null) {
            provider = createProvider(entity.getProviderType(), entity.getRegion(), entity.getProviderAccessKey(), entity.getProviderSecretKey());
        }
        cdnProviders.put(entity.getId(), provider);
        return provider;
    }

    private CDNProvider createProvider(CloudProviderType providerType, String region, String accessKey, String secretKey) {
        switch (providerType) {
            case ALI:
                return new AliyunCDNProvider(accessKey, secretKey);
            case TENCENT:
                return new TencentCDNProvider(accessKey, secretKey);
            case AWS:
                return new AWSCDNProvider(accessKey, secretKey, region);
            case BAIDU:
                return new BaiduCDNProvider(accessKey, secretKey);
            default:
                throw new ResponseException(ProviderError.E_CDN_PROVIDER_NOT_SUPPORT);
        }
    }

    public String sign(String url, long expiresInSeconds) {
        log.info("Sign CDN Url: {}, {}", url, expiresInSeconds);

        return URLSignerFactory.sign(domain -> {
            CDNDomainEntity entity = ensureExists(domain);
            String signKey = entity.getMainSignKey();
            if (StringUtils.isEmpty(signKey)) {
                throw new ResponseException(ProviderError.E_CDN_SIGN, "No sign key");
            }
            DomainSignInfo info = new DomainSignInfo();
            info.setProviderType(entity.getProviderType());
            info.setSignType(CDNSignType.fromName(entity.getSignType()));
            info.setSignKey(signKey);
            info.setDomainType(CDNDomainType.fromValue(entity.getType()));
            return info;
        }, url, expiresInSeconds);

    }

    private CDNDomainEntity ensureExists(String domain) {
        CDNDomainEntity entity = getDomainEntity(domain);
        if (entity == null) {
            log.error("Domain not found: {}", domain);
            throw new ResponseException(ProviderError.E_CDN_DOMAIN_NOT_EXISTS);
        }
        return entity;
    }

    public CDNRefreshTask checkRefreshStatus(String taskId) {
        CDNDomainRefreshTaskEntity task = cdnDomainRefreshTaskRepository.findByTaskId(taskId);
        if (task == null)
            throw new ResponseException(ProviderError.E_CDN_REFRESH, "Task ID not exists");
        return createTaskFromEntity(task);
    }

    private CDNRefreshTask createTaskFromEntity(CDNDomainRefreshTaskEntity entity) {
        CDNRefreshTask task = new CDNRefreshTask();
        task.setDomain(entity.getDomain());
        task.setPath(entity.getPath());
        task.setTaskId(entity.getTaskId());
        task.setStatus(CDNRefreshStatus.fromValue(entity.getStatus()));
        task.setProcess(entity.getProcess());
        task.setErrorMsg(entity.getErrorMsg());
        task.setType(CDNRefreshType.fromName(entity.getType()));
        task.setCreateTime(entity.getCommitTime());
        task.setCompleteTime(entity.getCompleteTime());
        return task;
    }

    public CDNRefreshTask checkRefreshStatus(String domain, String taskId) {
        CDNDomainEntity entity = ensureExists(domain);
        CDNProvider provider = getProvider(entity);
        try {
            return provider.checkStatus(domain, taskId);
        } catch (Exception e) {
            throw new ResponseException(ProviderError.E_CDN, "查询状态出错");
        }
    }


    public void processRefreshTask() {
        List<Integer> status = Arrays.asList(CDNRefreshStatus.REFRESHING.getValue(), CDNRefreshStatus.COMMITTED.getValue());
        cdnDomainRefreshTaskRepository.findByStatusIn(status).forEach(this::processTask);
    }

    private void processTask(CDNDomainRefreshTaskEntity task) {
        CDNDomainEntity entity = getDomainEntity(task.getDomain());
        CDNProvider provider = getProvider(entity);
        try {
            CDNRefreshTask refreshTask = provider.checkStatus(task.getDomain(), task.getTaskId());
            if (refreshTask == null)
                return;
            CDNRefreshStatus status = refreshTask.getStatus();
            task.setProcess(refreshTask.getProcess());
            if (status == CDNRefreshStatus.COMPLETE) {
                task.setProcess("100%");
                task.setCompleteTime(DateTimeUtil.now());
            } else if (status == CDNRefreshStatus.FAILED) {
                task.setErrorMsg(refreshTask.getErrorMsg());
            } else if (status == CDNRefreshStatus.REFRESHING) {
                task.setProcess(refreshTask.getProcess());
            }
            task.setStatus(status.getValue());
            cdnDomainRefreshTaskRepository.save(task);
        } catch (Exception e) {
            log.error("Check CDN Refresh Task Status Error: {}", e.getMessage());
        }
    }


    public List<CDNDomainSchedDTO> getAllSched() {
        return new ArrayList<>(domainSchedMap.values());
    }
}
