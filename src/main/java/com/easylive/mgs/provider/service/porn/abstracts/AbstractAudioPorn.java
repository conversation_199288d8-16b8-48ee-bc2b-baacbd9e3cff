package com.easylive.mgs.provider.service.porn.abstracts;

import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.rpc.http.ResponseException;
import com.google.gson.reflect.TypeToken;
import com.tencentcloudapi.ams.v20201229.models.*;
import com.tencentcloudapi.common.AbstractClient;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.JsonResponseModel;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;


/**
 * <AUTHOR>
 */
@Slf4j
public class AbstractAudioPorn extends AbstractClient {

    private static String endpoint = "ams.tencentcloudapi.com";
    private static String version = "2020-12-29";


    public AbstractAudioPorn(Credential credential, String region) {
        this(credential, region, new ClientProfile());
    }

    public AbstractAudioPorn(Credential credential, String region, ClientProfile profile) {
        super(AbstractAudioPorn.endpoint, AbstractAudioPorn.version, credential, region, profile);
    }


    /**
     * 创建音频任务
     * @param req
     * @return
     * @throws TencentCloudSDKException
     */
    public CreateAudioModerationTaskResponse createTask(CreateAudioModerationTaskRequest req) {
        JsonResponseModel<CreateAudioModerationTaskResponse> rsp = null;
        String rspStr = "";
        try {
            Type type = new TypeToken<JsonResponseModel<CreateAudioModerationTaskResponse>>() {
            }.getType();
            rspStr = this.internalRequest(req, "CreateAudioModerationTask");
            rsp  = gson.fromJson(rspStr, type);
        } catch (TencentCloudSDKException e) {
            log.error("Create tencent task fail: {}", e.toString());
            throw new ResponseException(ProviderError.E_TENCENT_CREATE);
        }
        log.info("Create tencent porn task response:{}", rspStr);
        return rsp.response;
    }

    /**
     * 取消音频任务
     * @param req
     * @return
     * @throws TencentCloudSDKException
     */
    public CancelTaskResponse cancelTask(CancelTaskRequest req) {
        JsonResponseModel<CancelTaskResponse> rsp = null;
        String rspStr = "";
        try {
            Type type = new TypeToken<JsonResponseModel<CancelTaskResponse>>() {
            }.getType();
            rspStr = this.internalRequest(req, "CancelTask");
            rsp  = gson.fromJson(rspStr, type);
        } catch (TencentCloudSDKException e) {
            log.error("Cancel tencent task fail: {}", e.toString());
            throw new ResponseException(ProviderError.E_TENCENT_CANCEL);
        }
        log.info("Cancel tencent porn task response:{}", rspStr);
        return rsp.response;
    }


    /**
     *
     * @param req
     * @return
     */
    public DescribeTaskDetailResponse describeTaskDetail(DescribeTaskDetailRequest req) {
        JsonResponseModel<DescribeTaskDetailResponse> rsp = null;
        String rspStr = "";
        try {
            Type type = new TypeToken<JsonResponseModel<DescribeTaskDetailResponse>>() {
            }.getType();
            rspStr = this.internalRequest(req, "DescribeTaskDetail");
            rsp  = gson.fromJson(rspStr, type);
        } catch (TencentCloudSDKException e) {
            log.error("Get tencent task detail fail: {}", e.toString());
            throw new ResponseException(ProviderError.E_TENCENT_DETAIL);
        }
        log.info("Tencent porn task detail response:{}", rspStr);
        return rsp.response;
    }
}
