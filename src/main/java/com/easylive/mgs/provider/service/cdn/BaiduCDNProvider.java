package com.easylive.mgs.provider.service.cdn;

import com.baidubce.BceClientConfiguration;
import com.baidubce.auth.DefaultBceCredentials;
import com.baidubce.services.cdn.CdnClient;
import com.baidubce.services.cdn.model.*;
import com.baidubce.services.cdn.model.cache.PrefetchTask;
import com.easylive.mgs.provider.enums.CDNRefreshStatus;
import com.easylive.mgs.provider.pub.enums.CDNRefreshType;
import lombok.extern.slf4j.Slf4j;

import java.net.URL;

@Slf4j
public class BaiduCDNProvider implements CDNProvider {
    private CdnClient cdnClient = null;
    private static final String BCE_CDN_END_POINT = "http://cdn.baidubce.com";                     // CDN服务端接口地址


    public BaiduCDNProvider(String accessKeyId, String accessKeySecret) {
        try {
            BceClientConfiguration config = new BceClientConfiguration()
                    .withCredentials(new DefaultBceCredentials(accessKeyId, accessKeySecret))
                    .withEndpoint(BCE_CDN_END_POINT);
            cdnClient = new CdnClient(config);
        } catch (Exception e) {
            log.error("init baidu cdn client error", e);
        }
    }

    @Override
    public String refresh(String[] urls, CDNRefreshType type, boolean force) throws Exception {
        PurgeRequest request = new PurgeRequest();

        // 百度云区分目录刷新和文件刷新
        if (type == CDNRefreshType.DIRECTORY) {
            for (String url : urls) {
                request.addTask(new PurgeTask().withDirectory(url));
            }
        } else {
            for (String url : urls) {
                request.addTask(new PurgeTask().withUrl(url));
            }
        }

        PurgeResponse response = cdnClient.purge(request);
        log.info("refresh response taskId: {}", response.getId());
        return response.getId();
    }

    @Override
    public String preload(String[] urls) throws Exception {
        PrefetchRequest request = new PrefetchRequest();
        for (String url : urls) {
            request.addTask(new PrefetchTask().withUrl(url));
        }
        PrefetchResponse response = cdnClient.prefetch(request);
        return response.getId();
    }

    @Override
    public CDNRefreshTask checkStatus(String domain, String taskId) throws Exception {
        GetPurgeStatusRequest request = new GetPurgeStatusRequest().withId(taskId);
        GetPurgeStatusResponse response = cdnClient.getPurgeStatus(request);
        if (response != null && response.getDetails() != null && !response.getDetails().isEmpty()) {
            PurgeStatus taskInfo = response.getDetails().get(0);

            CDNRefreshTask refreshTask = new CDNRefreshTask();
            refreshTask.setTaskId(taskId);
            refreshTask.setStatus(mapStatus(taskInfo.getStatus()));

            String taskUrl = taskInfo.getTask().getUrl();
            try {
                URL url = new URL(taskUrl);
                refreshTask.setDomain(url.getHost());
                refreshTask.setPath(url.getPath());
            } catch (Exception e) {
                log.error("Parse URL error: {}", taskUrl, e);
            }

            refreshTask.setType(mapType(taskInfo.getTask().getType()));
            refreshTask.setCreateTime(taskInfo.getCreatedAt());
            refreshTask.setCompleteTime(taskInfo.getFinishedAt());
            refreshTask.setErrorMsg("");
            refreshTask.setProcess(String.valueOf(taskInfo.getProgress()));

            return refreshTask;
        }
        return null;
    }

    private CDNRefreshStatus mapStatus(String status) {
        // 百度云的状态映射
        switch (status.toLowerCase()) {
            case "completed":
                return CDNRefreshStatus.COMPLETE;
            case "in-progress":
                return CDNRefreshStatus.REFRESHING;
            case "failed":
                return CDNRefreshStatus.FAILED;
            default:
                return CDNRefreshStatus.UNKNOWN;
        }
    }

    private CDNRefreshType mapType(String type) {
        // 百度云区分目录和文件
        switch (type.toLowerCase()) {
            case "directory":
                return CDNRefreshType.DIRECTORY;
            case "file":
                return CDNRefreshType.FILE;
            default:
                return CDNRefreshType.FILE;
        }
    }
}