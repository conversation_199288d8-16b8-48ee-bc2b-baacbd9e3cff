package com.easylive.mgs.provider.service;

import com.easylive.mgs.provider.entity.CloudProviderAccessEntity;
import com.easylive.mgs.provider.entity.CloudProviderAccountEntity;
import com.easylive.mgs.provider.pub.enums.CloudProviderType;
import com.easylive.mgs.provider.pub.model.CloudProviderAccessDTO;
import com.easylive.mgs.provider.repo.provider.CloudProviderAccessRepository;
import com.easylive.mgs.provider.repo.provider.CloudProviderAccountRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/26
 */
@Slf4j
@Service
public class CloudProviderService {

    @Autowired
    private CloudProviderAccessRepository cloudProviderAccessRepository;

    @Autowired
    private CloudProviderAccountRepository cloudProviderAccountRepository;

    private volatile Map<String, CloudProviderAccessEntity> accessNameMap = new HashMap<>();
    private volatile Map<Integer, CloudProviderAccessEntity> accessIdMap = new HashMap<>();
    private volatile Map<Integer, CloudProviderAccountEntity> accountIdMap = new HashMap<>();

    @PostConstruct
    public void init() {
        load();
    }

    public synchronized void load() {
        Map<Integer, CloudProviderAccountEntity> tmpAccountMap = new HashMap<>();
        Map<String, CloudProviderAccessEntity> tmpAccessMap = new HashMap<>();
        Map<Integer, CloudProviderAccessEntity> tmpAccessIdMap = new HashMap<>();

        cloudProviderAccountRepository.findAll().forEach(account -> {
            tmpAccountMap.put(Math.toIntExact(account.getId()), account);
        });

        cloudProviderAccessRepository.findAll().forEach(access -> {
            CloudProviderAccountEntity account = tmpAccountMap.get(access.getProviderAccountId());
            if (account != null) {
                CloudProviderType type = CloudProviderType.fromName(account.getType());
                if (type != null) {
                    access.setProviderType(type);
                    tmpAccessMap.put(access.getName(), access);
                    tmpAccessIdMap.put(Math.toIntExact(access.getId()), access);
                    log.info("Add cloud provider access info: {},{}", access.getProviderType(), access.getName());
                } else {
                    log.error("Cloud provider access has invalid account type: {},{}", access.getName(), account.getType());
                }
            } else {
                log.error("Cloud provider access has invalid account ID: {},{}", access.getName(), access.getProviderAccountId());
            }
        });
        log.info("{} cloud provider account added", tmpAccountMap.size());
        log.info("{} cloud provider access added", tmpAccessMap.size());


        this.accessNameMap = tmpAccessMap;
        this.accountIdMap = tmpAccountMap;
        this.accessIdMap = tmpAccessIdMap;
    }

    public List<CloudProviderAccessDTO> getAllAccess() {
        return accessNameMap.values().stream().map(this::providerAccessEntityToDTO).collect(Collectors.toList());
    }

    public CloudProviderAccessDTO getAccessByName(String name) {
        return providerAccessEntityToDTO(accessNameMap.get(name));
    }

    public CloudProviderAccessDTO getAccessById(Integer id) {
        return providerAccessEntityToDTO(accessIdMap.get(id));
    }


    private CloudProviderAccessDTO providerAccessEntityToDTO(CloudProviderAccessEntity entity) {
        if (entity == null)
            return null;
        CloudProviderAccessDTO dto = new CloudProviderAccessDTO();
        dto.setId(Math.toIntExact(entity.getId()));
        dto.setName(entity.getName());
        dto.setAccessKey(entity.getAccessKey());
        dto.setSecretKey(entity.getSecretKey());
        dto.setDescription(entity.getDescription());
        dto.setType(entity.getProviderType().getName());
        dto.setRegion(entity.getRegion());
        return dto;
    }
}
