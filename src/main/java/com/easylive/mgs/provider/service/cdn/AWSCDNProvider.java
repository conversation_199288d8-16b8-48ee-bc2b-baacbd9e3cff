package com.easylive.mgs.provider.service.cdn;

import com.easylive.common.util.DateTimeUtil;
import com.easylive.mgs.provider.enums.CDNRefreshStatus;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.enums.CDNRefreshType;
import com.easylive.rpc.http.ResponseException;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.cloudfront.CloudFrontClient;
import software.amazon.awssdk.services.cloudfront.model.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class AWSCDNProvider implements CDNProvider {
     private final CloudFrontClient cloudFrontClient;

    public AWSCDNProvider(String accessKeyId, String accessKeySecret,String region) {
        AwsBasicCredentials awsCreds = AwsBasicCredentials.create(accessKeyId, accessKeySecret);
        this.cloudFrontClient = CloudFrontClient.builder()
                .region(Region.of(region))
                .credentialsProvider(StaticCredentialsProvider.create(awsCreds))
                .build();
    }

    @Override
    public String refresh(String[] urls, CDNRefreshType type, boolean force) throws Exception {
        if( urls.length == 0)
            throw new ResponseException(ProviderError.E_CDN_REFRESH,"Invalid url");
        String domain = extractDomainName(urls[0]);
        List<String> paths = preparePaths(List.of(urls), type);
        return createInvalidation(domain, paths);
    }

    @Override
    public String preload(String[] urls) {
        return "";
    }

    @Override
    public CDNRefreshTask checkStatus(String domain, String taskId) throws Exception {
        CDNRefreshTask task = new CDNRefreshTask();

        Invalidation invalidation = getInvalidationStatus(domain, taskId);
        task.setTaskId(taskId);
        task.setDomain(domain);
        task.setCreateTime(Date.from(invalidation.createTime()));
        task.setStatus(mapStatus(invalidation.status()));

        return task;
    }

    private String extractDomainName(String url) throws URISyntaxException {
        URI uri = new URI(url);
        String domain = uri.getHost();
        return domain.startsWith("www.") ? domain.substring(4) : domain;
    }

    private CDNRefreshStatus mapStatus(String status) {
        switch (status) {
            case "NoChange":
            case "Completed":
                return CDNRefreshStatus.COMPLETE;
            case "InProgress":
                return CDNRefreshStatus.REFRESHING;
            case "Failed":
                return CDNRefreshStatus.FAILED;
            default:
                return CDNRefreshStatus.UNKNOWN;
        }
    }

    private List<String> preparePaths(List<String> urls, CDNRefreshType type) throws URISyntaxException {
        List<String> paths = new ArrayList<>();
        for (String url : urls) {
            URI uri = new URI(url);
            String path = uri.getPath();
            if (path.isEmpty()) {
                path = "/";
            }
            if (path.endsWith("/") && type == CDNRefreshType.DIRECTORY) {
                paths.add(path + "*");
            } else {
                paths.add(path);
            }
        }
        return paths;
    }

    private String findDistributionId(String domainName) {
        ListDistributionsRequest request = ListDistributionsRequest.builder().build();
        ListDistributionsResponse response = cloudFrontClient.listDistributions(request);

        for (DistributionSummary distribution : response.distributionList().items()) {
            if (distribution.aliases().items().contains(domainName)) {
                return distribution.id();
            }
        }

        throw new RuntimeException("No distribution found for domain: " + domainName);
    }

    public String createInvalidation(String domainName, List<String> paths) {
        String distributionId = findDistributionId(domainName);
        return createInvalidationById(distributionId, paths);
    }

    private String createInvalidationById(String distributionId, List<String> paths) {
        try {
            InvalidationBatch invalidationBatch = InvalidationBatch.builder()
                    .callerReference(String.valueOf(System.currentTimeMillis()))
                    .paths(Paths.builder().items(paths).quantity(paths.size()).build())
                    .build();

            CreateInvalidationRequest invalidationRequest = CreateInvalidationRequest.builder()
                    .distributionId(distributionId)
                    .invalidationBatch(invalidationBatch)
                    .build();

            CreateInvalidationResponse invalidationResponse = cloudFrontClient.createInvalidation(invalidationRequest);
            return invalidationResponse.invalidation().id();
        } catch (CloudFrontException e) {
            log.error("Error refresh cloudfront: {}", e.awsErrorDetails().errorMessage());
            return null;
        }
    }

    public Invalidation getInvalidationStatus(String domainName, String invalidationId) {
        String distributionId = findDistributionId(domainName);
        return getInvalidationStatusById(distributionId, invalidationId);
    }

    private Invalidation getInvalidationStatusById(String distributionId, String invalidationId) {
        try {
            GetInvalidationRequest invalidationRequest = GetInvalidationRequest.builder()
                    .distributionId(distributionId)
                    .id(invalidationId)
                    .build();

            GetInvalidationResponse invalidationResponse = cloudFrontClient.getInvalidation(invalidationRequest);
            return invalidationResponse.invalidation();
        } catch (CloudFrontException e) {
            log.error("Error get cloudfront invalidation status{}",e.awsErrorDetails().errorMessage());
            return null;
        }
    }
}
