package com.easylive.mgs.provider.service.porn;

/**
 * <AUTHOR>
 */
public interface PornService<T, V, R> {

    /**
     * 浏览任务
     *
     * @param content
     * @return
     */
    default T scanTask(V content) {
        return null;
    }

    /**
     * 创建任务
     * @param bizId
     * @param content
     * @return
     */
    default R createTask(String bizId, String content) {
        return null;
    };

    /**
     * 取消任务
     * @param taskId
     * @return
     */
    default String cancelTask(String taskId) {
        return null;
    };

    /**
     * 查询任务详情
     * @param taskId
     * @return
     */
    default String describeTaskDetail(String taskId) {
        return null;
    };
}
