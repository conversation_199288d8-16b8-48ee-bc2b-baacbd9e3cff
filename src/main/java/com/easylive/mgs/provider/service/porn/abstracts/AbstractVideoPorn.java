package com.easylive.mgs.provider.service.porn.abstracts;


import com.easylive.common.util.JsonUtil;
import com.easylive.mgs.provider.config.OssConfig;
import com.easylive.mgs.provider.pub.model.PronScanDTO;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.green20220302.Client;
import com.aliyun.green20220302.models.VideoModerationRequest;
import com.aliyun.green20220302.models.VideoModerationResponse;
import com.aliyun.green20220302.models.VideoModerationResponseBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AbstractVideoPorn {

    @Autowired
    @Qualifier("AliScanVideoClient")
    private Client client;

    @Autowired
    private OssConfig ossConfig;

    /**
     * 创建任务
     * @param bizId
     * @param content
     * @return
     */
    public VideoModerationResponseBody.VideoModerationResponseBodyData createTask(String bizId, String content) {

        JSONObject serviceParameters = new JSONObject();
        serviceParameters.put("url", content);
        serviceParameters.put("callback", ossConfig.getPronVideoCallback());
        serviceParameters.put("seed", ossConfig.getPronVideoSeed());
        serviceParameters.put("cryptType", "SHA256");
        serviceParameters.put("dataId", bizId);

        VideoModerationRequest videoModerationRequest = new VideoModerationRequest();
        videoModerationRequest.setService("videoDetection");
        videoModerationRequest.setServiceParameters(serviceParameters.toJSONString());

        try {
            VideoModerationResponse response = client.videoModeration(videoModerationRequest);
            log.error("Ali create scan video task response. {}", JsonUtil.toString(response));
            if (response.getStatusCode() != 200) {
                return null;
            }

            VideoModerationResponseBody result = response.getBody();
            Integer code = result.getCode();
            if (code != 200) {
                return null;
            }

            return result.getData();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}
