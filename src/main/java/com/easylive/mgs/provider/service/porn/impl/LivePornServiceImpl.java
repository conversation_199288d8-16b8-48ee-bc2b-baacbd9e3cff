package com.easylive.mgs.provider.service.porn.impl;

import com.easylive.common.util.DateTimeUtil;
import com.easylive.common.util.JsonUtil;
import com.easylive.mgs.provider.entity.TencentPornEntity;
import com.easylive.mgs.provider.enums.PornBizTypeEnum;
import com.easylive.mgs.provider.enums.PornTaskStatusEnum;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.model.PronScanDTO;
import com.easylive.mgs.provider.repo.main.TencentPornRepository;
import com.easylive.mgs.provider.repo.read.TencentPornReadRepository;
import com.easylive.mgs.provider.service.RedisService;
import com.easylive.mgs.provider.service.porn.PornService;
import com.easylive.mgs.provider.service.porn.abstracts.AbstractLivePorn;
import com.easylive.rpc.http.ResponseException;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.vm.v20201229.models.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("LivePornService")
public class LivePornServiceImpl implements PornService {

    @Autowired
    private TencentPornReadRepository tencentPornReadRepository;

    @Autowired
    private TencentPornRepository tencentPornRepository;

    @Autowired
    private RedisService redisService;

    @Value("${tencent.cloud.seed}")
    private String seed;

    @Value("${tencent.cloud.video.callBackUrl}")
    private String callBackUrl;

    @Value("${tencent.cloud.video.bizType}")
    private String videoBizType;

    @Value("${tencent.cloud.secretId}")
    private String secretId;

    @Value("${tencent.cloud.secretKey}")
    private String secretKey;

    private Credential credential;

    @PostConstruct
    public void init() {
        credential = new Credential(secretId, secretKey);
    }


    /**
     * 创建订单
     * @param bizId
     * @param content
     * @return
     */
    @Override
    public PronScanDTO createTask(String bizId, String content) {

        String cacheKey = "Tencent:scan:video:task:" + DateTimeUtil.formatAsDate(new Date());
        Long value = redisService.incrementAndExpire(cacheKey, 1L, 86400*1000L);

        if (value > 10000L) {
            throw new ResponseException(ProviderError.E_TENCENT_TASK_LIMIT);
        }

        TencentPornEntity entity = tencentPornReadRepository.findTopByBizIdAndBizTypeOrderById(bizId, PornBizTypeEnum.LIVE_VIDEO.getType());
        if (entity != null) {
            throw new ResponseException(ProviderError.E_TENCENT_TASK_EXISTS);
        }

        AbstractLivePorn videoPorn = new AbstractLivePorn(credential, null);
        CreateVideoModerationTaskRequest request = new CreateVideoModerationTaskRequest();

        StorageInfo input = new StorageInfo();
        input.setType("URL");
        input.setUrl(content);

        TaskInput taskInput = new TaskInput();
        taskInput.setDataId(bizId);
        taskInput.setName(bizId);
        taskInput.setInput(input);


        TaskInput[] tasks = new TaskInput[1];
        tasks[0] = taskInput;

        request.setType("LIVE_VIDEO");
        request.setSeed(seed);
        request.setCallbackUrl(callBackUrl);
        request.setBizType(videoBizType);

        request.setTasks(tasks);
        CreateVideoModerationTaskResponse response = videoPorn.createTask(request);

        TaskResult result = response.getResults()[0];
        if (result == null) {
            log.error("Create tencent porn task result is empty. {}", bizId);
            throw new ResponseException(ProviderError.E_TENCENT_CREATE);
        }

        //写入数据库
        if ("OK".equals(result.getCode())) {
            TencentPornEntity createEntity = TencentPornEntity.builder()
                    .bizId(result.getDataId())
                    .bizType(PornBizTypeEnum.LIVE_VIDEO.getType())
                    .taskId(result.getTaskId())
                    .taskStatus(PornTaskStatusEnum.CREATE.getStatus())
                    .taskContent(content).build();
            tencentPornRepository.save(createEntity);
        }

        return new PronScanDTO(result.getTaskId(), bizId);
    }

    /**
     * 取消视频任务
     * @param taskId
     */
    @Override
    public String cancelTask(String taskId) {
        TencentPornEntity entity = tencentPornReadRepository.findTopByTaskIdAndBizTypeOrderById(taskId, PornBizTypeEnum.LIVE_VIDEO.getType());
        if (entity == null) {
            throw new ResponseException(ProviderError.E_TENCENT_TASK_NOT_EXISTS);
        }

        AbstractLivePorn videoPorn = new AbstractLivePorn(credential, null);

        CancelTaskRequest request = new CancelTaskRequest();
        request.setTaskId(entity.getTaskId());
        CancelTaskResponse response = videoPorn.cancelTask(request);
        if (response.getRequestId() == null) {
            log.error("Cancel tencent porn video task result is empty. {}", entity.getTaskId());
            throw new ResponseException(ProviderError.E_TENCENT_CANCEL);
        }
        entity.setTaskStatus(PornTaskStatusEnum.CANCEL.getStatus());
        tencentPornRepository.save(entity);
        return response.getRequestId();
    }


    /**
     * 获取详情
     * @param taskId
     * @return
     */
    @Override
    public String describeTaskDetail(String taskId) {
        TencentPornEntity entity = tencentPornReadRepository.findTopByTaskIdAndBizTypeOrderById(taskId, PornBizTypeEnum.LIVE_VIDEO.getType());
        if (entity == null) {
            throw new ResponseException(ProviderError.E_TENCENT_TASK_NOT_EXISTS);
        }

        AbstractLivePorn videoPorn = new AbstractLivePorn(credential, null);
        DescribeTaskDetailRequest request = new DescribeTaskDetailRequest();
        request.setTaskId(taskId);
        return JsonUtil.toString(videoPorn.describeTaskDetail(request));
    }
}
