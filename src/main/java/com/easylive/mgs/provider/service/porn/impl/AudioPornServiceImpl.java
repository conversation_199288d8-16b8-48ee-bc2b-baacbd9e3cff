package com.easylive.mgs.provider.service.porn.impl;

import com.easylive.common.util.JsonUtil;
import com.easylive.mgs.provider.entity.TencentPornEntity;
import com.easylive.mgs.provider.enums.PornBizTypeEnum;
import com.easylive.mgs.provider.enums.PornTaskStatusEnum;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.model.PronScanDTO;
import com.easylive.mgs.provider.repo.main.TencentPornRepository;
import com.easylive.mgs.provider.repo.read.TencentPornReadRepository;
import com.easylive.mgs.provider.service.porn.PornService;
import com.easylive.mgs.provider.service.porn.abstracts.AbstractAudioPorn;
import com.easylive.rpc.http.ResponseException;
import com.tencentcloudapi.ams.v20201229.models.*;
import com.tencentcloudapi.common.Credential;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;


/**
 * <AUTHOR>
 */
@Slf4j
@Service("AudioPornService")
public class AudioPornServiceImpl implements PornService {
    @Autowired
    private TencentPornReadRepository tencentPornReadRepository;

    @Autowired
    private TencentPornRepository tencentPornRepository;

    @Value("${tencent.cloud.secretId}")
    private String secretId;

    @Value("${tencent.cloud.secretKey}")
    private String secretKey;

    @Value("${tencent.cloud.seed}")
    private String seed;

    @Value("${tencent.cloud.audio.callBackUrl}")
    private String callBackUrl;

    @Value("${tencent.cloud.audio.bizType}")
    private String audioBizType;

    private Credential credential;

    @PostConstruct
    public void init() {
        credential = new Credential(secretId, secretKey);
    }

    @Override
    public PronScanDTO createTask(String bizId, String content) {

        TencentPornEntity entity = tencentPornReadRepository.findTopByBizIdAndBizTypeOrderById(bizId, PornBizTypeEnum.LIVE_AUDIO.getType());
        if (entity != null) {
            throw new ResponseException(ProviderError.E_TENCENT_TASK_EXISTS);
        }

        AbstractAudioPorn audioPorn = new AbstractAudioPorn(credential, null);
        CreateAudioModerationTaskRequest request = new CreateAudioModerationTaskRequest();

        StorageInfo input = new StorageInfo();
        input.setType("URL");
        input.setUrl(content);

        TaskInput taskInput = new TaskInput();
        taskInput.setDataId(bizId);
        taskInput.setName(bizId);
        taskInput.setInput(input);


        TaskInput [] tasks = new TaskInput[1];
        tasks[0] = taskInput;

        request.setType("LIVE_AUDIO");
        request.setSeed(seed);
        request.setCallbackUrl(callBackUrl);
        request.setBizType(audioBizType);

        request.setTasks(tasks);
        CreateAudioModerationTaskResponse response = audioPorn.createTask(request);

        TaskResult result = response.getResults()[0];
        if (result == null) {
            log.error("Create tencent porn audio task result is empty. {}", bizId);
            throw new ResponseException(ProviderError.E_TENCENT_CREATE);
        }

        //写入数据库
        if ("OK".equals(result.getCode())) {
            TencentPornEntity createEntity = TencentPornEntity.builder()
                    .bizId(result.getDataId())
                    .bizType(PornBizTypeEnum.LIVE_AUDIO.getType())
                    .taskId(result.getTaskId())
                    .taskStatus(PornTaskStatusEnum.CREATE.getStatus())
                    .taskContent(content).build();
            tencentPornRepository.save(createEntity);
        }

        return new PronScanDTO(result.getTaskId(), bizId);
    }

    @Override
    public String cancelTask(String taskId) {
        TencentPornEntity entity = tencentPornReadRepository.findTopByTaskIdAndBizTypeOrderById(taskId, PornBizTypeEnum.LIVE_AUDIO.getType());
        if (entity == null) {
            throw new ResponseException(ProviderError.E_TENCENT_TASK_NOT_EXISTS);
        }

        AbstractAudioPorn audioPorn = new AbstractAudioPorn(credential, null);

        CancelTaskRequest request = new CancelTaskRequest();
        request.setTaskId(entity.getTaskId());
        CancelTaskResponse response = audioPorn.cancelTask(request);
        if (response.getRequestId() == null) {
            log.error("Cancel tencent porn audio task result is empty. {}", entity.getTaskId());
            throw new ResponseException(ProviderError.E_TENCENT_CANCEL);
        }
        entity.setTaskStatus(PornTaskStatusEnum.CANCEL.getStatus());
        tencentPornRepository.save(entity);
        return response.getRequestId();
    }

    @Override
    public String describeTaskDetail(String taskId) {
        TencentPornEntity entity = tencentPornReadRepository.findTopByTaskIdAndBizTypeOrderById(taskId, PornBizTypeEnum.LIVE_AUDIO.getType());
        if (entity == null) {
            throw new ResponseException(ProviderError.E_TENCENT_TASK_NOT_EXISTS);
        }

        AbstractAudioPorn audioPorn = new AbstractAudioPorn(credential, null);
        DescribeTaskDetailRequest request = new DescribeTaskDetailRequest();
        request.setTaskId(taskId);
        return JsonUtil.toString(audioPorn.describeTaskDetail(request));
    }
}
