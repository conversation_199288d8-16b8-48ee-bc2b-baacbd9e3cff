package com.easylive.mgs.provider.repo.read;

import com.easylive.mgs.provider.entity.TencentPornEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022-05-27
 */
@Repository
public interface TencentPornReadRepository extends JpaRepository<TencentPornEntity, Long> {
    TencentPornEntity findTopByBizIdAndBizTypeOrderById(String bizId, Integer bizTyp);

    TencentPornEntity findTopByTaskIdAndBizTypeOrderById(String taskId, Integer bizTyp);
}
