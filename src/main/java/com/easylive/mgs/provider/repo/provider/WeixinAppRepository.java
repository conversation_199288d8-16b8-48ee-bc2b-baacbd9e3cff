package com.easylive.mgs.provider.repo.provider;


import com.easylive.mgs.provider.entity.WeixinAppEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.persistence.LockModeType;

@Repository
public interface WeixinAppRepository extends JpaRepository<WeixinAppEntity, Long> {
    WeixinAppEntity findByAppId(String appId);

    @Lock(value = LockModeType.PESSIMISTIC_WRITE)
    @Query(value = "from WeixinAppEntity where appId = ?1")
    WeixinAppEntity findByAppIdWithLock(String appId);
}
