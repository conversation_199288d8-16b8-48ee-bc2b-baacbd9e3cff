package com.easylive.mgs.provider.repo.provider;


import com.easylive.mgs.provider.entity.SensitiveWordsEntity;
import com.easylive.mgs.provider.pub.enums.SuggestionType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
public interface SensitiveWordsRepository extends JpaRepository<SensitiveWordsEntity, Long> {
    Boolean existsByWords(String words);

    @Query("select words from SensitiveWordsEntity where suggestion = :suggestion")
    List<String> listWordsBySuggestion(@Param("suggestion") String suggestion);

    @Query("from SensitiveWordsEntity where suggestion = :suggestion")
    List<SensitiveWordsEntity> listBySuggestion(String suggestion);

    @Transactional
    @Modifying
    @Query(value = "delete from sensitive_word where create_at < :expiredDate", nativeQuery = true)
    int delExpiredWords(@Param("expiredDate") Date expiredDate);
}
