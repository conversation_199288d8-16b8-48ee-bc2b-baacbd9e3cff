package com.easylive.mgs.provider.repo.provider;


import com.easylive.mgs.provider.entity.CDNDomainRefreshTaskEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CDNDomainRefreshTaskRepository extends JpaRepository<CDNDomainRefreshTaskEntity, Long> {
    List<CDNDomainRefreshTaskEntity> findByStatusIn(List<Integer> status);

    CDNDomainRefreshTaskEntity findByTaskId(String taskId);
}
