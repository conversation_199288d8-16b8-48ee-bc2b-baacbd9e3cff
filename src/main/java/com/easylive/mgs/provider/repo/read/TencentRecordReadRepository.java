package com.easylive.mgs.provider.repo.read;

import com.easylive.mgs.provider.entity.TencentRecordEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-27
 */
@Repository
public interface TencentRecordReadRepository extends JpaRepository<TencentRecordEntity, Long> {
    /**
     * 获取内容记录
     * @param vid
     * @param start
     * @param count
     * @return
     */
    @Query(value = "select * from t_tencent_record where vid = :vid order by id desc limit :start, :count ", nativeQuery = true)
    List<TencentRecordEntity> listRecordByVid(@Param("vid") String vid, @Param("start") int start, @Param("count") int count);


    /**
     * 获取内容记录
     * @param start
     * @param count
     * @return
     */
    @Query(value = "select * from t_tencent_record order by id desc limit :start, :count ", nativeQuery = true)
    List<TencentRecordEntity> listRecord(@Param("start") int start, @Param("count") int count);
}
