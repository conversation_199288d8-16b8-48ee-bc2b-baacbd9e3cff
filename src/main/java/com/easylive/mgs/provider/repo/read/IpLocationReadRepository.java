package com.easylive.mgs.provider.repo.read;


import com.easylive.mgs.provider.entity.IpLocationEntity;
import com.easylive.mgs.provider.repo.main.IpLocationRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/2 9:28 AM
 */
@Repository
public interface IpLocationReadRepository extends IpLocationRepository {

    Optional<IpLocationEntity> findByIp(String ip);
}
