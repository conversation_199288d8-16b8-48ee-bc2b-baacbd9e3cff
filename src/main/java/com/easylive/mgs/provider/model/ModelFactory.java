package com.easylive.mgs.provider.model;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/6/1
 */
public class ModelFactory {

    public static GreenImageScanResult getScanResultFromJsonObject(JSONObject object) {
        GreenImageScanResult ret = new GreenImageScanResult();
        ret.setScene(object.getString("scene"));
        ret.setLabel(object.getString("label"));
        ret.setSuggestion(object.getString("suggestion"));
        ret.setRate(object.getFloat("rate"));
        return ret;
    }

}
