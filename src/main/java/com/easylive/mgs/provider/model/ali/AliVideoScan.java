package com.easylive.mgs.provider.model.ali;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/11 7:55 PM
 * @version 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
public class AliVideoScan {

    @JsonProperty("Code")
    private Integer code;

    @JsonProperty("Data")
    private Body body;

    @JsonProperty("Message")
    private String message;

    @JsonProperty("RequestId")
    private String requestId;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Body {

        @JsonProperty("DataId")
        private String dataId;

        private String taskId;

        @JsonProperty("AudioResult")
        private AudioResult audioResult;

        @JsonProperty("FrameResult")
        private ImageResult imageResult;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AudioResult {
        @JsonProperty("SliceDetails")
        private List<AudioDetail> sliceDetails;

        private Integer sliceNum;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImageResult {
        @JsonProperty("FrameNum")
        private Integer frameNum;

        @JsonProperty("Frames")
        private List<FramesDetail> frames;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AudioDetail {
        @JsonProperty("EndTime")
        private Integer endTime;

        @JsonProperty("EndTimestamp")
        private Long endTimestamp;

        @JsonProperty("Extend")
        private String extend;

        @JsonProperty("Labels")
        private String labels;

        @JsonProperty("RiskTips")
        private String riskTips;

        @JsonProperty("RiskWords")
        private String riskWords;

        @JsonProperty("StartTime")
        private Integer startTime;

        @JsonProperty("StartTimestamp")
        private Long startTimestamp;

        @JsonProperty("Text")
        private String text;

        @JsonProperty("Url")
        private String url;

        @JsonProperty("subTaskId")
        private String subTaskId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FramesDetail {
        @JsonProperty("Offset")
        private Integer offset;

        @JsonProperty("Results")
        private List<ResultsDetail> results;

        @JsonProperty("TempUrl")
        private String tempUrl;

        private String subTaskId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResultsDetail {

        @JsonProperty("Result")
        private List<Result> result;

        @JsonProperty("Service")
        private String service;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result {
        @JsonProperty("Confidence")
        private float confidence;

        @JsonProperty("Label")
        private String label;
    }
}
