package com.easylive.mgs.provider.model.tencent;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TencentIpLocation {

    private Integer status;
    private String message;

    @JsonProperty(value = "request_id")
    private String requestId;

    private Result result;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result {
        private String ip;
        private Location location;

        @JsonProperty(value = "ad_info")
        private AdInfo adInfo;


        @Data
        public static class Location {
            private Double lat;
            private Double lng;
        }


        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class AdInfo {
            private String nation;
            private String province;
            private String city;
            private String district;
            private Integer adcode;

            @JsonProperty(value = "nation_code")
            private Integer nationCode;
        }
    }

}
