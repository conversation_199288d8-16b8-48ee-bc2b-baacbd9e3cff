package com.easylive.mgs.provider.model.baidu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/12/2 5:28 PM
 * @version 1.0
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResultDetail {

    @JsonProperty("formatted_address")
    private String formattedAddress;

    private AddressComponent addressComponent;

    private int cityCode;

}
