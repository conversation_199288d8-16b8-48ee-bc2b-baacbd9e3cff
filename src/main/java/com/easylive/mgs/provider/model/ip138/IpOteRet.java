package com.easylive.mgs.provider.model.ip138;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> wgl
 * @ClassName : IpOteRet
 * @date : Created in 2020/10/23 12:22 下午
 * @version: 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class IpOteRet {
    private String ret;
    private String ip;
    private List<String> data;
}
