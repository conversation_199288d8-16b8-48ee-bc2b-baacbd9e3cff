package com.easylive.mgs.provider.controller;


import com.easylive.common.util.IdUtil;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.enums.CDNRefreshType;
import com.easylive.mgs.provider.pub.model.CDNDomainDTO;
import com.easylive.mgs.provider.pub.model.CDNDomainSchedDTO;
import com.easylive.mgs.provider.service.CDNDomainService;
import com.easylive.mgs.provider.service.cdn.CDNRefreshTask;
import com.easylive.rpc.http.ResponseException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "CDN服务")
@Slf4j
@RestController
@RequestMapping(value = "/cdn")
public class CDNController {

    @Autowired
    private CDNDomainService cdnDomainService;


    @ApiOperation(value = "")
    @RequestMapping(value = "/domain/list", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public List<CDNDomainDTO> listAll() {
        return cdnDomainService.listAll();
    }

    @ApiOperation(value = "")
    @RequestMapping(value = "/refresh", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public String refresh(@ApiParam(value = "URL") @RequestParam String url,
                          @ApiParam(value = "刷新类型") @RequestParam(required = false, defaultValue = "file") String type,
                          @ApiParam(value = "是否强制刷新目录下其他资源") @RequestParam(required = false, defaultValue = "false") boolean force
    ) {
        CDNRefreshType refreshType = CDNRefreshType.fromName(type);
        if (refreshType == null)
            throw new ResponseException(ProviderError.E_PROVIDER, "Invalid CDN Refresh Type");
        return cdnDomainService.refresh(url, refreshType, force);
    }

    @ApiOperation(value = "根据任务ID查询任务状态")
    @RequestMapping(value = "/refresh/status", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public CDNRefreshTask checkRefreshStatus(@ApiParam(value = "任务ID") @RequestParam String taskId) {
        return cdnDomainService.checkRefreshStatus(taskId);
    }

    @ApiOperation(value = "根据域名和任务ID直接从云厂商查询状态")
    @RequestMapping(value = "/refresh/status/direct", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public CDNRefreshTask checkRefreshStatus(@ApiParam(value = "域名") @RequestParam String domain,
                                             @ApiParam(value = "任务ID") @RequestParam String taskId) {
        return cdnDomainService.checkRefreshStatus(domain, taskId);
    }




    @ApiOperation(value = "")
    @RequestMapping(value = "/sign", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public String sign(@ApiParam(value = "url") @RequestParam String url,
                       @ApiParam(value = "额外有效时间，默认0秒") @RequestParam(name = "expires", required = false, defaultValue = "0") long expiresInSeconds) {
        return cdnDomainService.sign(url, expiresInSeconds);
    }

    @ApiOperation(value = "")
    @RequestMapping(value = "/sched/list", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public List<CDNDomainSchedDTO> schedList() {
        return cdnDomainService.getAllSched();
    }


}
