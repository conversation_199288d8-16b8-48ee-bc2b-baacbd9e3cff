package com.easylive.mgs.provider.controller;


import com.easylive.mgs.provider.pub.model.WeiXinUserPhoneDTO;
import com.easylive.mgs.provider.pub.model.WeixinGenerateSchemeDTO;
import com.easylive.mgs.provider.pub.model.WeixinJscode2SessionDTO;
import com.easylive.mgs.provider.service.weixin.WeixinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "微信服务")
@Slf4j
@RestController
@RequestMapping(value = "/weixin")
public class WeixinController {

    @Autowired
    private WeixinService weixinService;

    @ApiOperation(value = "生成小程序链接")
    @RequestMapping("/applet/scheme")
    public WeixinGenerateSchemeDTO generateScheme(@ApiParam(value = "小程序APP ID") @RequestParam String appId,
                                                  @ApiParam(value = "小程序路径") @RequestParam(required = false) String path,
                                                  @ApiParam(value = "小程序参数") @RequestParam(required = false) String query,
                                                  @ApiParam(value = "小程序环境参数") @RequestParam(required = false, defaultValue = "release") String env,
                                                  @ApiParam(value = "有效时间，单位分钟，1-43200分钟(30天)") @RequestParam(required = false,defaultValue = "1440") int validTime) {
        String url = weixinService.generateScheme(appId, path, query, env, validTime);
        WeixinGenerateSchemeDTO dto = new WeixinGenerateSchemeDTO();
        dto.setUrl(url);
        return dto;
    }

    @ApiOperation(value = "小程序服务端登录验证")
    @RequestMapping("/applet/jscode2session")
    public WeixinJscode2SessionDTO generateScheme(@ApiParam(value = "小程序APP ID") @RequestParam String appId,
                                                  @ApiParam(value = "小程序登录的code") @RequestParam String code) {
        return weixinService.jscode2Session(appId,code);
    }

    @ApiOperation(value = "小程序服务端获取用户手机号")
    @RequestMapping("/applet/getPhoneNumber")
    public WeiXinUserPhoneDTO getPhoneNumber(@ApiParam(value = "小程序APP ID") @RequestParam String appId,
                                             @ApiParam(value = "小程序登录的code") @RequestParam String code) {
        return weixinService.getPhoneNumber(appId, code);
    }

}
