package com.easylive.mgs.provider.controller;

import com.easylive.mgs.provider.pub.model.PronScanDTO;
import com.easylive.mgs.provider.service.porn.PornService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Api(tags = "阿里云鉴黄服务")
@Slf4j
@RestController
@RequestMapping(value = "/ali/porn")
public class AliPornController {

    @Autowired
    @Qualifier("VideoPornService")
    private PornService videoPornService;


    @ApiOperation(value = "视频检查")
    @PostMapping("/video/scan")
    @ResponseBody
    public PronScanDTO voiceScan(@RequestParam String bizId,
                                @RequestParam String content) {
        return (PronScanDTO) videoPornService.createTask(bizId, content);
    }
}
