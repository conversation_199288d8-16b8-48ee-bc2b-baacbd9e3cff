package com.easylive.mgs.provider.controller;


import com.easylive.mgs.provider.model.GreenImageScanResult;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.enums.DetectionServiceEnum;
import com.easylive.mgs.provider.pub.enums.ImageModerationEnum;
import com.easylive.mgs.provider.pub.model.ScanDTO;
import com.easylive.mgs.provider.service.PornService;
import com.easylive.rpc.http.BizException;
import com.easylive.rpc.http.ResponseCommonError;
import com.easylive.rpc.http.ResponseException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "鉴黄服务")
@Slf4j
@RestController
@RequestMapping(value = "/porn")
public class PornController {

    @Autowired
    private PornService pornService;


    @ApiOperation(value = "图片同步鉴定")
    @RequestMapping("/image/scan/ali")
    @ResponseBody
    public GreenImageScanResult imageScanAli(@ApiParam(value = "图片链接") @RequestParam String imageUrl) {
        return pornService.imageCheck(imageUrl);
    }

    @ApiOperation(value = "图片同步鉴定")
    @RequestMapping("/image/scan")
    @ResponseBody
    public GreenImageScanResult imageScan(@ApiParam(value = "图片链接") @RequestParam String imageUrl) {
        return pornService.aliPorn(imageUrl);
    }

    @ApiOperation(value = "图片广告鉴定")
    @RequestMapping("/image/scan/ad")
    @ResponseBody
    public GreenImageScanResult imageScanAd(@ApiParam(value = "图片链接") @RequestParam String imageUrl) {
        return pornService.imageAdCheck(imageUrl);
    }

    @ApiOperation(value = "文字鉴定")
    @RequestMapping("/text/scan")
    @ResponseBody
    public GreenImageScanResult textScan(@ApiParam(value = "文字") @RequestParam String content) {
        return pornService.textScan(content);
    }


    @ApiOperation(value = "文字鉴定")
    @RequestMapping("/text/scan/enhance")
    @ResponseBody
    public ScanDTO textScanEnhance(
            @ApiParam(value = "文字") @RequestParam String content,
            @ApiParam(value = "检查服务") @RequestParam DetectionServiceEnum detectionService
            ) {
        content = content.trim();
        if (content.length() >= 600) {
            throw new ResponseException(ResponseCommonError.E_PARAM, "鉴定内容长度不能超过600");
        }
        ScanDTO dto = pornService.textEnhanceScan(content, detectionService);
        log.info("Text scan enhance content {} detectionService {} result {}", content, detectionService, dto);
        return dto;
    }


    @ApiOperation(value = "图片增强鉴定")
    @PostMapping("/image/scan/enhance")
    @ResponseBody
    public GreenImageScanResult imageScanEnhance(
            @ApiParam(value = "图片公网地址") @RequestParam String url,
            @ApiParam(value = "检查服务") @RequestParam ImageModerationEnum imageModeration,
            @ApiParam(value = "业务数据") @RequestParam(required = false) String dataId
    ) {
        return pornService.imageScanEnhance(url, imageModeration, dataId);
    }

    /*@ApiOperation(value = "OSS图片增强鉴定")
    @PostMapping("/image/scan/enhanceFormOss")
    @ResponseBody
    public AliImageEnhanceScanDTO imageScanEnhanceFormOss(
            @ApiParam(value = "图片名") @RequestParam String fileName,
            @ApiParam(value = "oss模块名（桶）") @RequestParam String ossModel,
            @ApiParam(value = "检查服务") @RequestParam ImageModerationEnum imageModeration,
            @ApiParam(value = "业务数据") @RequestParam(required = false) String dataId
    ) {
        return pornService.imageScanEnhanceFormOss(fileName, imageModeration, ossModel, dataId);
    }*/
}
