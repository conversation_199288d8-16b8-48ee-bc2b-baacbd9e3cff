package com.easylive.mgs.provider.controller;

import com.easylive.mgs.provider.service.IdCardService;
import com.google.common.base.Strings;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping(value = "/id/card")
public class IDCardController {

    @Autowired
    private IdCardService idCardService;

    @ApiOperation(value = "校验二元素")
    @RequestMapping(value = "/meta/verify",method = RequestMethod.POST)
    @ResponseBody
    public Boolean IDCardMetaVerify(@RequestParam String idNo,
                                    @RequestParam String name) {
        if (Strings.isNullOrEmpty(idNo) || Strings.isNullOrEmpty(name)) {
            return false;
        }

        //判断是否身份证号
        if (!idNo.matches("^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$")) {
            log.error("idNo不合法:{}", idNo);
            return false;
        }

        if (idNo.length() != 18) {
            log.error("idNo长度不对:{}", idNo);
            return false;
        }

        if (name.length() < 2 || name.length() > 10) {
            return false;
        }

        return idCardService.metaVerify(idNo, name);
    }
}
