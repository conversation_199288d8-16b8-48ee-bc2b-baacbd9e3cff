package com.easylive.mgs.provider.controller;


import com.easylive.mgs.provider.pub.model.location.GpsLocation;
import com.easylive.mgs.provider.pub.model.location.IpLocation;
import com.easylive.mgs.provider.service.LocationService;
import com.easylive.rpc.http.model.EmptyResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "位置服务")
@Slf4j
@RestController
@RequestMapping(value = "/location")
public class LocationController {

    @Autowired
    private LocationService locationService;



    @ApiOperation(value = "通过经纬度获取位置")
    @RequestMapping(value = "/gps",method = RequestMethod.GET)
    @ResponseBody
    public GpsLocation getLocationByGPS(@ApiParam(value = "经度") @RequestParam Double longitude,
                                        @ApiParam(value = "纬度") @RequestParam Double latitude) {

        return locationService.getLocationByGPS(longitude,latitude);
    }

    @ApiOperation(value = "通过IP获取位置")
    @RequestMapping(value = "/ip",method = RequestMethod.GET)
    @ResponseBody
    public IpLocation getLocationByIp(@ApiParam(value = "ip地址") @RequestParam String ip) {

        return locationService.getLocationByIp(ip);
    }

    @ApiOperation(value = "通过IP获取位置")
    @RequestMapping(value = "/iptest",method = RequestMethod.GET)
    @ResponseBody
    public LocationService.IpLocationInfo getLocationByIp1(@ApiParam(value = "ip地址") @RequestParam String ip) {

        return locationService.getLocationFromIp138(ip);
        //return  EmptyResponse.INSTANCE;
    }
}
