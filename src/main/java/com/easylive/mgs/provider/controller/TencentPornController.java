package com.easylive.mgs.provider.controller;

import com.easylive.mgs.provider.entity.TencentRecordEntity;
import com.easylive.mgs.provider.pub.model.PronScanDTO;
import com.easylive.mgs.provider.pub.model.TencentVoiceAO;
import com.easylive.mgs.provider.pub.model.TencentVoiceDTO;
import com.easylive.mgs.provider.repo.read.TencentRecordReadRepository;
import com.easylive.mgs.provider.service.porn.PornService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@Api(tags = "腾讯鉴黄服务")
@Slf4j
@RestController
@RequestMapping(value = "/tencent/porn")
public class TencentPornController {

    @Autowired
    @Qualifier("LivePornService")
    private PornService videoPornService;

    @Autowired
    @Qualifier("AudioPornService")
    private PornService audioPornService;


    @Autowired
    @Qualifier("VoicePornService")
    private PornService voicePornService;

    @Autowired
    private TencentRecordReadRepository tencentPornReadRepository;


    @ApiOperation(value = "一句话语音识别")
    @PostMapping("/voice/scan")
    @ResponseBody
    public TencentVoiceDTO voiceScan(TencentVoiceAO voiceAO) {
        return (TencentVoiceDTO) voicePornService.scanTask(voiceAO);
    }

    @ApiOperation(value = "音频异步鉴定")
    @PostMapping("/audio/scan")
    @ResponseBody
    public PronScanDTO audioScan(
            @ApiParam(value = "业务ID") @RequestParam String bizId,
            @ApiParam(value = "业务内容") @RequestParam String content
            ) {
        return (PronScanDTO) audioPornService.createTask(bizId, content);
    }

    @ApiOperation(value = "音频取消鉴定")
    @PostMapping("/audio/cancel")
    public void audioCancel(@ApiParam(value = "任务ID") @RequestParam String taskId) {
        audioPornService.cancelTask(taskId);
    }

    @ApiOperation(value = "音频详情")
    @GetMapping("/audio/detail")
    @ResponseBody
    public String audioDetail(@ApiParam(value = "任务ID") @RequestParam String taskId) {
        return audioPornService.describeTaskDetail(taskId);
    }

    @ApiOperation(value = "视频异步鉴定")
    @PostMapping("/video/scan")
    @ResponseBody
    public PronScanDTO videoScan(
            @ApiParam(value = "业务ID") @RequestParam String bizId,
            @ApiParam(value = "业务内容") @RequestParam String content
    ) {
        return (PronScanDTO) videoPornService.createTask(bizId, content);
    }

    @ApiOperation(value = "视频取消鉴定")
    @PostMapping("/video/cancel")
    public void videoCancel(@ApiParam(value = "任务ID") @RequestParam String taskId) {
        videoPornService.cancelTask(taskId);
    }

    @ApiOperation(value = "视频详情")
    @GetMapping("/video/detail")
    @ResponseBody
    public String videoDetail(@ApiParam(value = "任务ID") @RequestParam String taskId) {
        return videoPornService.describeTaskDetail(taskId);
    }

    @ApiOperation(value = "查询视频记录")
    @PostMapping("/list/record")
    public List<TencentRecordEntity> listTencentRecord(
            @ApiParam(value = "视频集合") @RequestParam(required = false) String vid,
            @ApiParam(value = "分页开始索引") @RequestParam Integer start,
            @ApiParam(value = "每页数据行") @RequestParam Integer count
            ) {
        return  (vid != null && !vid.isBlank()) ? tencentPornReadRepository.listRecordByVid(vid, start, count) : tencentPornReadRepository.listRecord(start, count);
    }
}
