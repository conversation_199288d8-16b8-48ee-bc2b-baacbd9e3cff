package com.easylive.mgs.provider.controller;

import com.easylive.common.event.EventProducer;
import com.easylive.common.util.DateTimeUtil;
import com.easylive.common.util.JsonUtil;
import com.easylive.mgs.provider.config.OssConfig;
import com.easylive.mgs.provider.entity.TencentRecordEntity;
import com.easylive.mgs.provider.enums.PornBizTypeEnum;
import com.easylive.mgs.provider.enums.SuggestionType;
import com.easylive.mgs.provider.model.ali.AliVideoScan;
import com.easylive.mgs.provider.pub.ProviderError;
import com.easylive.mgs.provider.pub.enums.PornEnum;
import com.easylive.mgs.provider.pub.enums.PornTypeEnum;
import com.easylive.mgs.provider.pub.event.MultiPornEvent;
import com.easylive.mgs.provider.pub.event.PornEvent;
import com.easylive.mgs.provider.repo.main.TencentRecordRepository;
import com.easylive.rpc.http.ResponseException;
import com.tencentcloudapi.ams.v20201229.models.*;
import com.tencentcloudapi.common.AbstractModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "回调服务")
@Slf4j
@RestController
@RequestMapping(value = "/notify")
public class NotifyController {
    @Value("${tencent.cloud.seed}")
    private String tencentSeed;

    @Autowired
    private OssConfig ossConfig;

    @Autowired
    private EventProducer eventProducer;

    @Autowired
    private TencentRecordRepository tencentRecordRepository;

    @ApiOperation(value = "音频鉴定回调")
    @RequestMapping("/tencent/porn/audio")
    @ResponseBody
    public void tencentAudioNotify(@RequestBody(required = false) String body, HttpServletRequest request) {
        if (Strings.isEmpty(body)) {
            return;
        }

        String sign = request.getHeader("X-Signature");
        log.info("Tencent porn audio notify sign:{},  body:{}", sign, body);
        String bodySign = DigestUtils.sha256Hex(tencentSeed + body);
        if (!bodySign.equals(sign)) {
            log.error("Tencent porn audio notify sign not match!");
            return;
        }

        DescribeTaskDetailResponse detail = AbstractModel.fromJsonString(body, DescribeTaskDetailResponse.class);
        if (!detail.getStatus().equals("RUNNING")) {
            return;
        }

        AudioSegments[] audioSegments = detail.getAudioSegments();
        if (audioSegments == null || audioSegments.length == 0) {
            return;
        }

        AudioResult audioResult = null;
        try {
            audioResult = audioSegments[0].getResult();
        } catch (Exception e) {
            log.error("Tencent porn audio notify not exists audio result. bizId: {}, reqId: {}", detail.getDataId(), detail.getRequestId());
            return;
        }

        String vid = detail.getDataId().split("_")[0];
        TencentRecordEntity entity = TencentRecordEntity.builder()
                .vid(vid).bizType(PornBizTypeEnum.LIVE_AUDIO.getType())
                .bizId(detail.getDataId())
                .taskId(detail.getTaskId())
                .suggestion(audioResult.getSuggestion())
                .score(Math.toIntExact(audioResult.getScore()))
                .label(audioResult.getLabel()).subLabel(audioResult.getSubLabel())
                .content(audioResult.getText())
                .contentUrl(audioResult.getUrl())
                .createTime(DateTimeUtil.now())
                .updateTime(DateTimeUtil.now())
                .build();
        tencentRecordRepository.save(entity);

        try {
            PornEvent event = new PornEvent(PornTypeEnum.AUDIO, detail.getDataId(), detail.getTaskId(), audioResult.getSuggestion(),
                    audioResult.getLabel(), audioResult.getSubLabel(), Math.toIntExact(audioResult.getScore()), audioResult.getUrl());
            eventProducer.emitAsync(PornEnum.TOPIC_NAME, detail.getDataId(), PornEnum.TENCENT.getValue(), event);
        } catch (Exception e) {
            log.error("Tencent porn audio notify send event error.", e);
        }
    }

    @ApiOperation(value = "直播视频鉴定回调")
    @RequestMapping("/tencent/porn/video")
    @ResponseBody
    public void tencentLiveNotify(@RequestBody(required = false) String body, HttpServletRequest request) {
        if (Strings.isEmpty(body)) {
            return;
        }

        String sign = request.getHeader("X-Signature");
        log.info("Tencent porn video notify sign:{},  body:{}", sign, body);
        String bodySign = DigestUtils.sha256Hex(tencentSeed + body);
        if (!bodySign.equals(sign)) {
            log.error("Tencent porn video notify sign not match!");
            return;
        }

        /**
         * 这里很坑音频和视频同样的model却是不同的包, 也可以整合通过bizType区分
         */
        com.tencentcloudapi.vm.v20201229.models.DescribeTaskDetailResponse detail =
                AbstractModel.fromJsonString(body, com.tencentcloudapi.vm.v20201229.models.DescribeTaskDetailResponse.class);
        if (!detail.getStatus().equals("RUNNING")) {
            return;
        }

        com.tencentcloudapi.vm.v20201229.models.AudioSegments[] audioSegments = detail.getAudioSegments();
        if (audioSegments != null && audioSegments.length > 0) {
            com.tencentcloudapi.vm.v20201229.models.AudioResult audioResult = null;
            try {
                audioResult = audioSegments[0].getResult();
            } catch (Exception e) {
                log.error("Tencent porn video notify not exists audio result. bizId: {}, reqId: {}", detail.getDataId(), detail.getRequestId());
                return;
            }

            try {
                PornEvent event = new PornEvent(PornTypeEnum.AUDIO, detail.getDataId(), detail.getTaskId(), audioResult.getSuggestion(),
                        audioResult.getLabel(), audioResult.getSubLabel(), Math.toIntExact(audioResult.getScore()), audioResult.getUrl());
                eventProducer.emitAsync(PornEnum.TOPIC_NAME, detail.getDataId(), PornEnum.TENCENT.getValue(), event);
            } catch (Exception e) {
                log.error("Tencent porn video notify send event error.", e);
            }
        }

        com.tencentcloudapi.vm.v20201229.models.ImageSegments[] imageSegments = detail.getImageSegments();
        if (imageSegments != null && imageSegments.length > 0) {
            com.tencentcloudapi.vm.v20201229.models.ImageResult imageResult = null;
            try {
                imageResult = imageSegments[0].getResult();
            } catch (Exception e) {
                log.error("Tencent porn video notify not exists audio result. bizId: {}, reqId: {}", detail.getDataId(), detail.getRequestId());
                return;
            }

            try {
                PornEvent event = new PornEvent(PornTypeEnum.IMAGE, detail.getDataId(), detail.getTaskId(), imageResult.getSuggestion(),
                        imageResult.getLabel(), imageResult.getSubLabel(), Math.toIntExact(imageResult.getScore()), imageResult.getUrl());
                eventProducer.emitAsync(PornEnum.TOPIC_NAME, detail.getDataId(), PornEnum.TENCENT.getValue(), event);
            } catch (Exception e) {
                log.error("Tencent porn video notify send event error.", e);
            }
        }
    }


    @ApiOperation(value = "点播视频鉴定回调")
    @RequestMapping("/ali/porn/video")
    @ResponseBody
    public void aliVideoNotify(HttpServletRequest request) {
        String content = request.getParameter("content");
        String checksum = request.getParameter("checksum");
        //log.info("Ali porn video notify checksum:{} content:{}", checksum, content);

        String signStr = ossConfig.getAccessUid() + ossConfig.getPronVideoSeed() + content;
        String sha256Hex = DigestUtils.sha256Hex(signStr);
        if (!sha256Hex.equals(checksum)) {
            log.error("Ali porn video notify sign not match. {} {}", checksum, sha256Hex);
            throw new ResponseException(ProviderError.E_ALI_SIGN);
        }

        AliVideoScan scanContent = JsonUtil.fromString(content, AliVideoScan.class);
        AliVideoScan.Body body = scanContent.getBody();
        List<AliVideoScan.FramesDetail> imageResult =  body.getImageResult() == null ? List.of() : body.getImageResult().getFrames();
        List<AliVideoScan.AudioDetail> audioResult = body.getAudioResult() == null ? List.of() : body.getAudioResult().getSliceDetails();

        if (scanContent.getCode() != 200 || (imageResult.isEmpty() && audioResult.isEmpty())) {

            MultiPornEvent event = new MultiPornEvent(PornTypeEnum.VIDEO, body.getDataId(), body.getTaskId(), List.of());
            eventProducer.emitAsync(PornEnum.TOPIC_NAME, body.getDataId(), PornEnum.ALI.getValue(), event);

            TencentRecordEntity entity = TencentRecordEntity.builder()
                    .vid(body.getDataId()).bizType(PornBizTypeEnum.VIDEO.getType())
                    .bizId(body.getDataId())
                    .taskId(body.getTaskId())
                    .suggestion(SuggestionType.PASS.getValue())
                    .score(99)
                    .label("")
                    .content("")
                    .contentUrl("")
                    .createTime(DateTimeUtil.now())
                    .updateTime(DateTimeUtil.now())
                    .build();
            tencentRecordRepository.save(entity);
            return;
        }

        ArrayList<MultiPornEvent.Data> list = new ArrayList<>();
        ArrayList<TencentRecordEntity> listRecord = new ArrayList<>();
        if (!imageResult.isEmpty()) {
            imageResult.forEach(v -> {
                AliVideoScan.Result result = v.getResults().get(0).getResult().get(0);
                Integer score = (int) result.getConfidence();
                list.add(new MultiPornEvent.Data(PornTypeEnum.IMAGE, SuggestionType.BLOCK.getValue(), result.getLabel(), "", score, v.getTempUrl()));

                TencentRecordEntity entity = TencentRecordEntity.builder()
                        .vid(body.getDataId()).bizType(PornBizTypeEnum.VIDEO.getType())
                        .bizId(body.getDataId())
                        .taskId(body.getTaskId())
                        .suggestion(SuggestionType.BLOCK.getValue())
                        .score(score)
                        .label(result.getLabel())
                        .content("")
                        .contentUrl(v.getTempUrl())
                        .createTime(DateTimeUtil.now())
                        .updateTime(DateTimeUtil.now())
                        .build();
                listRecord.add(entity);
            });
        }

        if (!audioResult.isEmpty()) {
            audioResult.forEach(v -> {
                list.add(new MultiPornEvent.Data(PornTypeEnum.AUDIO, SuggestionType.BLOCK.getValue(), v.getLabels(), "", 95, v.getText()));
                TencentRecordEntity entity = TencentRecordEntity.builder()
                        .vid(body.getDataId()).bizType(PornBizTypeEnum.VIDEO.getType())
                        .bizId(body.getDataId())
                        .taskId(body.getTaskId())
                        .suggestion(SuggestionType.BLOCK.getValue())
                        .score(99)
                        .label(v.getLabels())
                        .content(v.getText())
                        .contentUrl("")
                        .createTime(DateTimeUtil.now())
                        .updateTime(DateTimeUtil.now())
                        .build();
                listRecord.add(entity);
            });
        }

        // tencentRecordRepository 批量入库
        tencentRecordRepository.saveAll(listRecord);

        MultiPornEvent event = new MultiPornEvent(PornTypeEnum.VIDEO, body.getDataId(), body.getTaskId(), list);
        eventProducer.emitAsync(PornEnum.TOPIC_NAME, body.getDataId(), PornEnum.ALI.getValue(), event);
        log.info("Ali porn video event :{}", event);
    }
}
