package com.easylive.mgs.provider.controller;

import com.easylive.mgs.provider.service.CDNDomainService;
import com.easylive.mgs.provider.service.CloudProviderService;
import lombok.Data;
import org.ehcache.core.statistics.CacheStatistics;
import org.ehcache.core.statistics.TierStatistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/9/23
 */
@RestController
@RequestMapping("/admin")
public class AdminController {
    @Qualifier("TextCacheStat")
    @Autowired
    private CacheStatistics textCacheStatistics;

    @Autowired
    private CDNDomainService cdnDomainService;

    @Autowired
    private CloudProviderService cloudProviderService;

    @RequestMapping(value = "/cdn/reload", method = RequestMethod.GET)
    public void reloadCDN() {
        cdnDomainService.load();
    }

    @RequestMapping(value = "/cloud/reload", method = RequestMethod.GET)
    public void reloadCloudProvider() {
        cloudProviderService.load();
    }

    @RequestMapping(value = "/cache/text/status", method = RequestMethod.GET)
    public CacheStat test() {
        return new CacheStat(textCacheStatistics);
    }

    @Data
    private static class CacheStat {
        private long evictions;
        private long expirations;
        private long gets;
        private float hitPercentage;
        private long hits;
        private long misses;
        private float missPercentage;
        private long removals;
        private long puts;
        private long objectCount;
        private long byteAllocated;
        private long byteOccupied;

        CacheStat(CacheStatistics stat) {
            evictions = stat.getCacheEvictions();
            expirations = stat.getCacheExpirations();
            gets = stat.getCacheGets();
            hitPercentage = stat.getCacheHitPercentage();
            hits = stat.getCacheHits();
            misses = stat.getCacheMisses();
            missPercentage = stat.getCacheMissPercentage();
            puts = stat.getCachePuts();
            removals = stat.getCacheRemovals();
            TierStatistics ts = stat.getTierStatistics().get("OnHeap");
            byteAllocated = ts.getAllocatedByteSize();
            byteOccupied = ts.getOccupiedByteSize();
            objectCount = ts.getMappings();
            // log.info("{}",((DefaultCacheStatistics)stat).getKnownStatistics().get("OnHeap:MappingCount").value());
            //  stat.getTierStatistics();
        }
    }
}
