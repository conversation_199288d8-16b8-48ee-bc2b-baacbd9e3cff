package com.easylive.mgs.provider.controller;


import com.easylive.mgs.provider.pub.model.CloudProviderAccessDTO;
import com.easylive.mgs.provider.service.CloudProviderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "云厂商访问信息相关服务")
@Slf4j
@RestController
@RequestMapping(value = "/cloud")
public class CloudProviderController {

    @Autowired
    private CloudProviderService cloudProviderService;


    @ApiOperation(value = "")
    @RequestMapping(value = "/access/list", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public List<CloudProviderAccessDTO> listAll() {
        return cloudProviderService.getAllAccess();
    }
}
