package com.easylive.mgs.provider.entity;

import com.easylive.mgs.provider.pub.enums.CloudProviderType;
import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/6/26
 */
@Entity
@Data
@Table(name = "cdn_domain")
public class CDNDomainEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;

    private int type;

    @Column(name = "access_id")
    private Integer providerAccessId;

    @Column(name = "sign_key")
    private String mainSignKey;

    @Column(name = "sign_key2")
    private String SecondarySignKey;

    @Column(name = "sign_type")
    private String signType;

    private String description;

    @Transient
    private CloudProviderType providerType;

    @Transient
    private String providerAccessKey;

    @Transient
    private String providerSecretKey;

    @Transient
    private String region;

    @Transient
    private String providerName;
}
