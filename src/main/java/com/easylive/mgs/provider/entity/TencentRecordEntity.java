package com.easylive.mgs.provider.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/05/27
 */
@Data
@Entity
@Builder
@DynamicUpdate
@DynamicInsert
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_tencent_record")
public class TencentRecordEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String vid;

    /**
     * @see com.easylive.mgs.provider.enums.PornBizTypeEnum
     */
    @Column(name = "biz_type")
    private Integer bizType;

    @Column(name = "biz_id")
    private String bizId;

    @Column(name = "task_id")
    private String taskId;

    private String suggestion;

    private Integer score;

    private String label;

    @Column(name = "sub_label")
    private String subLabel;

    private String content;

    @Column(name = "content_url")
    private String contentUrl;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}
