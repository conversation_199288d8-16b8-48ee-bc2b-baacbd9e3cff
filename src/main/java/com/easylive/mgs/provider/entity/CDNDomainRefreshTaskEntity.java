package com.easylive.mgs.provider.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/6/26
 */
@Entity
@Data
@Table(name = "cdn_domain_refresh_task")
public class CDNDomainRefreshTaskEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_id")
    private String taskId;

    @Column(name = "domain")
    private String domain;

    @Column(name = "provider")
    private String provider;

    @Column(name = "path")
    private String path;

    @Column(name = "type")
    private String type;

    @Column(name = "status")
    private int status;

    @Column(name = "process")
    private String process;

    @Column(name = "error_msg")
    private String errorMsg;

    @Column(name = "commit_time")
    private Date commitTime;

    @Column(name = "complete_time")
    private Date completeTime;
}
