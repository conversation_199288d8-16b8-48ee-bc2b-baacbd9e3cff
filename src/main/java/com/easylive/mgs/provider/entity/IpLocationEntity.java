package com.easylive.mgs.provider.entity;

import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/12/1 3:42 PM
 * @version 1.0
 */
@Entity
@Data
@DynamicUpdate
@DynamicInsert
@Table(name = "t_iplookup")
public class IpLocationEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String ip;
    private String country;
    private String province;
    private String city;
    private String isp;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "update_time")
    private Date updateTime;
}
