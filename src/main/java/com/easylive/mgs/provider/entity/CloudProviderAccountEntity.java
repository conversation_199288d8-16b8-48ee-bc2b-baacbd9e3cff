package com.easylive.mgs.provider.entity;

import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Entity
@Data
@Table(name = "cloud_provider_account")
public class CloudProviderAccountEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 云服务商类型，例如ali,tencent,huoshan
     */
    private String type;

    /**
     * 云账号描述
     */
    private String description;
}
