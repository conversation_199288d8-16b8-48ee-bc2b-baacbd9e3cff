package com.easylive.mgs.provider.entity;

import javax.persistence.*;

import lombok.Data;

/**
 * CDN域名调度项实体类
 */
@Data
@Entity
@Table(name = "cdn_domain_sched_item")
public class CdnDomainSchedItemEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "sched_id", nullable = false)
    private Long schedId;

    @Column(name = "domain_id", nullable = false)
    private Long domainId;

    @Column(name = "weight", nullable = false)
    private Integer weight;
}