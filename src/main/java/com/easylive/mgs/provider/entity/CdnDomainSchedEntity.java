package com.easylive.mgs.provider.entity;

import javax.persistence.*;
import lombok.Data;

/**
 * CDN域名调度实体类
 */
@Data
@Entity
@Table(name = "cdn_domain_sched")
public class CdnDomainSchedEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name", length = 64, nullable = false, unique = true)
    private String name;
    
    @Column(name = "enabled", nullable = false)
    private Boolean enabled;
    
    @Column(name = "valid_time", length = 64)
    private String validTime;
    
    @Column(name = "description", length = 64, nullable = false)
    private String description;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "sched_id")
    private java.util.List<CdnDomainSchedItemEntity> items;
    
}