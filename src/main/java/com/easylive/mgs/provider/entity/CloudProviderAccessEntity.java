package com.easylive.mgs.provider.entity;

import com.easylive.mgs.provider.pub.enums.CloudProviderType;
import lombok.Data;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2024/6/25
 */
@Entity
@Data
@Table(name = "cloud_provider_access")
public class CloudProviderAccessEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 云服务商账号ID
     */
    @Column(name = "account_id")
    private Integer providerAccountId;

    /**
     * 访问信息唯一标识符，方便记忆
     */
    private String name;

    /**
     * 访问AK
     */
    @Column(name = "access_key")
    private String accessKey;

    /**
     * 访问SK
     */
    @Column(name = "secret_key")
    private String secretKey;

    @Column(name = "region")
    private String region;

    /**
     * 描述
     */
    private String description;

    @Transient
    private CloudProviderType providerType;
}
