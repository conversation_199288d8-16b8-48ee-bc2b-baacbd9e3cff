package com.easylive.mgs.provider.entity;

import com.easylive.mgs.provider.pub.enums.DetectionLabelsEnum;
import com.easylive.mgs.provider.pub.enums.DetectionServiceEnum;
import lombok.Data;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;


@Entity
@Data
@Table(name = "sensitive_word")
@DynamicInsert
@DynamicUpdate
public class SensitiveWordsEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String labels;

    private String words;

    private String tips;

    private String suggestion;

    @Column(name = "create_at")
    private Date createTime;

    @Column(name = "updated_at")
    private Date updatedTime;
}
