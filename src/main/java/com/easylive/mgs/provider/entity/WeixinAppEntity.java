package com.easylive.mgs.provider.entity;

import lombok.Data;
import org.checkerframework.checker.units.qual.C;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;


@Entity
@Data
@Table(name = "weixin_app")
public class WeixinAppEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Column(name = "app_id")
    private String appId;

    @Column(name="secret")
    private String secretKey;

    @Column(name = "type")
    private int type;

    @Column(name = "expire_time")
    private Date expireTime;

    @Column(name = "access_token")
    private String accessToken;

    @Column(name = "comment")
    private String comment;
}
