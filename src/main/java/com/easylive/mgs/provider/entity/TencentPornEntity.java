package com.easylive.mgs.provider.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.C;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/05/27
 */
@Data
@Entity
@Builder
@DynamicUpdate
@DynamicInsert
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_tencent_porn")
public class TencentPornEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "biz_id")
    private String bizId;

    /**
     * @see com.easylive.mgs.provider.enums.PornBizTypeEnum
     */
    @Column(name = "biz_type")
    private Integer bizType;

    @Column(name = "task_id")
    private String taskId;

    /**
     * @see com.easylive.mgs.provider.enums.PornTaskStatusEnum
     */
    @Column(name = "task_status")
    private Integer taskStatus;

    @Column(name = "task_content")
    private String taskContent;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}
