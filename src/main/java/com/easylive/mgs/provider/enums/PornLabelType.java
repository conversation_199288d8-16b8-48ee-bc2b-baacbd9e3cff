package com.easylive.mgs.provider.enums;

/**
 * 图片检测返回模型
 *
 * <AUTHOR>
 * @date 2020/6/1
 */
public enum PornLabelType {

    /**
     * normal：正常图片，无色情内容
     * sexy：性感图片
     * porn：色情图片
     */
    NORMAL("normal", "正常图片，无色情内容"),
    SEXY("sexy", "性感图片"),
    PORN("porn", "色情图片");

    private String value;

    private String message;

    PornLabelType(String value, String message) {
        this.value = value;
        this.message = message;
    }

    public String getValue() {
        return value;
    }

    public String getMessage() {
        return message;
    }

}
