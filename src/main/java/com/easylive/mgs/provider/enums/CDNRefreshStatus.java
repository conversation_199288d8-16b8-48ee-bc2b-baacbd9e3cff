package com.easylive.mgs.provider.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/4
 */
@Getter
public enum CDNRefreshStatus {
    COMMITTED("committed", 0),
    REFRESHING("refreshing", 1),
    COMPLETE("complete", 2),
    FAILED("failed",3),
    UNKNOWN("unknown", 4);

    private final String name;
    private final int value;

    CDNRefreshStatus(String status, int value) {
        this.name = status;
        this.value = value;
    }

    public static CDNRefreshStatus fromName(String status) {
        for (CDNRefreshStatus value : CDNRefreshStatus.values()) {
            if (value.getName().equals(status)) {
                return value;
            }
        }
        return null;
    }

    public static CDNRefreshStatus fromValue(int value) {
        for (CDNRefreshStatus status : CDNRefreshStatus.values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        return null;
    }
}
