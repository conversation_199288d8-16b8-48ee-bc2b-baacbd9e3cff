server.port = 8080
spring.profiles.active=online



#spring boot admin
#spring.boot.admin.client.url=http://localhost:8080
#management.endpoints.web.exposure.include=*

# Database

spring.datasource.jdbc-url=*************************************************************************************************************************************************
spring.datasource.username=yizhibo
spring.datasource.password=Yizhibo2015
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.maximum-pool-size=4
spring.datasource.minimum-idle=1

spring.datasource.secondary.jdbc-url=*******************************************************************************************************************************************************
spring.datasource.secondary.username=yizhibo
spring.datasource.secondary.password=Yizhibo2015
spring.datasource.secondary.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.secondary.maximum-pool-size=16
spring.datasource.secondary.minimum-idle=1


spring.datasource.provider.jdbc-url=*****************************************************************************************************************************************************
spring.datasource.provider.username=yizhibo
spring.datasource.provider.password=Yizhibo2015
spring.datasource.provider.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.provider.maximum-pool-size=4
spring.datasource.provider.minimum-idle=1





# Redis
spring.redis.host=main.redis.yizhibo.tv
spring.redis.port=6379
spring.redis.password=Yizhibo2015
spring.redis.lettuce.pool.min-idle=1
spring.redis.lettuce.pool.max-active=1

# OSS配置
oss.porn.access.key.id = LTAI5t6Bh84GGugYNQQS6cBF
oss.porn.access.key.secret = ******************************
oss.porn.endpoint = green-vpc.cn-beijing.aliyuncs.com
oss.auth.endpoint = cloudauth-vpc.cn-beijing.aliyuncs.com
oss.porn.video.endpoint = green-cip-vpc.cn-beijing.aliyuncs.com
oss.porn.region = cn-beijing
oss.porn.video.seed = 805a9f7a179cc81f3a15cf61a6a52a35
oss.porn.video.callback = http://notify.sageunion.net/provider/ali/porn/video
oss.access.uid = 1645893208571625

# 服务监控
spring.application.name=provider
management.endpoints.web.exposure.include=*
management.metrics.tags.application=${spring.application.name}
# 注意此处使用，访问的时候必须访问/actuator/prometheus,如不配置则访问/prometheus
management.endpoints.web.base-path=/actuator

# 腾讯云监控
tencent.cloud.secretId = AKIDTfZDeqoNjqjPp2tSGo67qJWkgL0Pl99t
tencent.cloud.secretKey = iBOL3Wk8F6fwz82ey4v4xbz8RTTUIqV0
tencent.cloud.audio.callBackUrl = http://notify.sageunion.net/provider/tencent/porn/audio
tencent.cloud.video.callBackUrl = http://notify.sageunion.net/provider/tencent/porn/video
tencent.cloud.seed = f3a33d8fda29b47894680fd4555e883bb0a390a4
tencent.cloud.audio.bizType = live_audio
tencent.cloud.video.bizType = live_video

# kafka
kafka.server.bootstrap=ke4:9092,ke5:9092,ke6:9092
kafka.consumer.group-id=provider-service

spring.cloud.nacos.discovery.server-addr=nacos1:8848,nacos2:8848,nacos3:8848

# Feign Client配置
ribbon.ReadTimeout=10000
ribbon.ConnectTimeout=10000
ribbon.ServerListRefreshInterval=5000
#feign.compression.request.enabled=true
#feign.compression.request.min-request-size=2048

server.undertow.accesslog.enabled=true
# 日志打印文件地址
server.undertow.accesslog.dir=/logs
# 日志打印文件名称
server.undertow.accesslog.prefix=access_log.
server.undertow.accesslog.file-date-format=.yyyy-MM-dd
# 日志输出表达式,默认值是common
server.undertow.accesslog.pattern=%a - %l %t "%r" %s %b %T "%{i,Referer}" "%{i,User-Agent}" %{i,X-Forwarded-For} "%A:%p" "" ""
server.undertow.options.server.record-request-start-time=true
server.undertow.accesslog.rotate=true

spring.main.allow-bean-definition-overriding=true

system.zookeeper.hosts = zk7:2181,zk8:2181,zk9:2181

weixin.token.service.enabled = true

