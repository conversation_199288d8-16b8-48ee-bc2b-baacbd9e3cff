package com.easylive.mgs.provider.util;

import com.easylive.mgs.provider.pub.util.UrlUtil;
import org.junit.Assert;
import org.junit.Test;

/**
 * UrlUtil.encodeURL 函数的详细单元测试
 * 测试 RFC 3986 标准合规性和各种边界情况
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class TestUrlUtil {

    @Test
    public void testBasicUrlEncoding() throws Exception {
        // 基本URL编码测试
        String url = "https://example.com/path/file.txt?param=value";
        String result = UrlUtil.encodeURL(url);
        Assert.assertEquals("https://example.com/path/file.txt?param=value", result);
        System.out.println("基本URL编码: " + result);
    }

    @Test
    public void testChineseCharacterEncoding() throws Exception {
        // 中文字符编码测试
        String url = "https://example.com/文档目录/测试文件.txt?name=张三&search=java编程";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com/%E6%96%87%E6%A1%A3%E7%9B%AE%E5%BD%95/%E6%B5%8B%E8%AF%95%E6%96%87%E4%BB%B6.txt?name=%E5%BC%A0%E4%B8%89&search=java%E7%BC%96%E7%A8%8B";
        Assert.assertEquals(expected, result);
        System.out.println("中文字符编码: " + result);
    }

    @Test
    public void testSpecialCharacterEncoding() throws Exception {
        // 特殊字符编码测试
        String url = "https://example.com/path with spaces/file[1].txt?param=value with spaces&other=<EMAIL>";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com/path%20with%20spaces/file%5B1%5D.txt?param=value%20with%20spaces&other=<EMAIL>";
        Assert.assertEquals(expected, result);
        System.out.println("特殊字符编码: " + result);
    }

    @Test
    public void testFragmentEncoding() throws Exception {
        // Fragment编码测试
        String url = "https://example.com/page.html?param=value#section 1";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com/page.html?param=value#section%201";
        Assert.assertEquals(expected, result);
        System.out.println("Fragment编码: " + result);
    }

    @Test
    public void testComplexUrlWithAllComponents() throws Exception {
        // 包含所有组件的复杂URL测试
        String url = "https://user:<EMAIL>:8080/文档/测试 文件.pdf?name=张三&age=25&hobby=编程#第一章";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://user:<EMAIL>:8080/%E6%96%87%E6%A1%A3/%E6%B5%8B%E8%AF%95%20%E6%96%87%E4%BB%B6.pdf?name=%E5%BC%A0%E4%B8%89&age=25&hobby=%E7%BC%96%E7%A8%8B#%E7%AC%AC%E4%B8%80%E7%AB%A0";
        Assert.assertEquals(expected, result);
        System.out.println("复杂URL编码: " + result);
    }

    @Test
    public void testRootPathEncoding() throws Exception {
        // 根路径编码测试
        String url = "https://example.com/";
        String result = UrlUtil.encodeURL(url);
        Assert.assertEquals("https://example.com/", result);
        System.out.println("根路径编码: " + result);
    }

    @Test
    public void testEmptyQueryParameter() throws Exception {
        // 空查询参数测试
        String url = "https://example.com/path?param1=&param2=value&param3";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com/path?param1=&param2=value&param3";
        Assert.assertEquals(expected, result);
        System.out.println("空查询参数编码: " + result);
    }

    @Test
    public void testUrlWithoutProtocol() throws Exception {
        // 无协议URL测试
        String url = "example.com/path/file.txt?param=value";
        String result = UrlUtil.encodeURL(url);
        String expected = "example.com/path/file.txt?param=value";
        Assert.assertEquals(expected, result);
        System.out.println("无协议URL编码: " + result);
    }

    @Test
    public void testUrlWithoutPath() throws Exception {
        // 无路径URL测试
        String url = "https://example.com?param=value";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com?param=value";
        Assert.assertEquals(expected, result);
        System.out.println("无路径URL编码: " + result);
    }

    @Test
    public void testUrlWithPort() throws Exception {
        // 带端口URL测试
        String url = "https://example.com:8080/path/文件.txt";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com:8080/path/%E6%96%87%E4%BB%B6.txt";
        Assert.assertEquals(expected, result);
        System.out.println("带端口URL编码: " + result);
    }

    @Test
    public void testAlreadyEncodedUrl() throws Exception {
        // 已编码URL测试（防止重复编码）
        String url = "https://example.com/path/%E6%96%87%E6%A1%A3?param=%E5%80%BC";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com/path/%E6%96%87%E6%A1%A3?param=%E5%80%BC";
        Assert.assertEquals(expected, result);
        System.out.println("已编码URL: " + result);
    }

    @Test
    public void testReservedCharactersInPath() throws Exception {
        // 路径中保留字符测试
        String url = "https://example.com/path:with@special/chars.txt";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com/path:with@special/chars.txt";
        Assert.assertEquals(expected, result);
        System.out.println("路径保留字符: " + result);
    }

    @Test
    public void testReservedCharactersInQuery() throws Exception {
        // 查询参数中保留字符测试
        String url = "https://example.com/path?url=https://other.com/path&param=value";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com/path?url=https://other.com/path&param=value";
        Assert.assertEquals(expected, result);
        System.out.println("查询保留字符: " + result);
    }

    @Test
    public void testMultipleConsecutiveSlashes() throws Exception {
        // 多个连续斜杠测试
        String url = "https://example.com//path//to///file.txt";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com//path//to///file.txt";
        Assert.assertEquals(expected, result);
        System.out.println("连续斜杠: " + result);
    }

    @Test
    public void testUnicodeCharacters() throws Exception {
        // Unicode字符测试
        String url = "https://example.com/测试/🚀/file.txt?emoji=🎉&text=测试";
        String result = UrlUtil.encodeURL(url);
        String expected = "https://example.com/%E6%B5%8B%E8%AF%95/%F0%9F%9A%80/file.txt?emoji=%F0%9F%8E%89&text=%E6%B5%8B%E8%AF%95";
        Assert.assertEquals(expected, result);
        System.out.println("Unicode字符: " + result);
    }

    @Test
    public void testNullAndEmptyInputs() throws Exception {
        // 空值和null输入测试
        Assert.assertNull(UrlUtil.encodeURL(null));
        Assert.assertEquals("", UrlUtil.encodeURL(""));
        System.out.println("空值处理: 通过");
    }

    @Test
    public void testRfc3986Compliance() throws Exception {
        // RFC 3986 标准合规性测试
        
        // 测试 unreserved 字符不被编码
        String url1 = "https://example.com/ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~";
        String result1 = UrlUtil.encodeURL(url1);
        Assert.assertEquals(url1, result1);
        
        // 测试 sub-delims 在路径中不被编码
        String url2 = "https://example.com/path!$&'()*+,;=test";
        String result2 = UrlUtil.encodeURL(url2);
        Assert.assertEquals(url2, result2);
        
        // 测试 pchar 在路径中不被编码
        String url3 = "https://example.com/path:@test";
        String result3 = UrlUtil.encodeURL(url3);
        Assert.assertEquals(url3, result3);
        
        System.out.println("RFC 3986 合规性测试: 通过");
    }

    @Test
    public void testEdgeCases() throws Exception {
        // 边界情况测试
        
        // 只有协议和域名
        String url1 = "https://example.com";
        String result1 = UrlUtil.encodeURL(url1);
        Assert.assertEquals("https://example.com", result1);
        
        // 只有域名和查询参数
        String url2 = "example.com?param=value";
        String result2 = UrlUtil.encodeURL(url2);
        Assert.assertEquals("example.com?param=value", result2);
        
        // 只有域名和fragment
        String url3 = "example.com#section";
        String result3 = UrlUtil.encodeURL(url3);
        Assert.assertEquals("example.com#section", result3);
        
        // 空路径但有查询参数
        String url4 = "https://example.com?param=value";
        String result4 = UrlUtil.encodeURL(url4);
        Assert.assertEquals("https://example.com?param=value", result4);
        
        System.out.println("边界情况测试: 通过");
    }

    @Test
    public void testQiniuExampleCompatibility() throws Exception {
        // 与七牛云例子兼容性测试
        String path = "/DIR1/中文/vodfile.mp4";
        String encodedPath = UrlUtil.encodeURL(path);
        String expected = "/DIR1/%E4%B8%AD%E6%96%87/vodfile.mp4";
        Assert.assertEquals(expected, encodedPath);
        System.out.println("七牛云兼容性: " + encodedPath);
    }

    @Test
    public void testPerformance() throws Exception {
        // 性能测试
        String url = "https://example.com/测试路径/文件.txt?参数=值&other=test#锚点";
        
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            UrlUtil.encodeURL(url);
        }
        long endTime = System.currentTimeMillis();
        
        System.out.println("性能测试: 10000次编码耗时 " + (endTime - startTime) + "ms");
        Assert.assertTrue("编码性能应该在合理范围内", (endTime - startTime) < 5000);
    }

    @Test
    public void testEncodeURLComponent_basic() {
        // 普通英文
        Assert.assertEquals("abc123", UrlUtil.encodeURLComponent("abc123"));
        // 空格
        Assert.assertEquals("hello%20world", UrlUtil.encodeURLComponent("hello world"));
        // 中文
        Assert.assertEquals("%E4%B8%AD%E6%96%87", UrlUtil.encodeURLComponent("中文"));
        // emoji
        Assert.assertEquals("%F0%9F%9A%80", UrlUtil.encodeURLComponent("🚀"));
        // 特殊符号
        Assert.assertEquals("%21%2A%27%28%29%3B%3A%40%26%3D%2B%24%2C%2F%3F%23%5B%5D", UrlUtil.encodeURLComponent("!*'();:@&=+$,/?#[]"));
        // 已经编码的内容不会重复编码
        Assert.assertEquals("abc%2520def", UrlUtil.encodeURLComponent("abc%20def"));        // 空字符串
        Assert.assertEquals("", UrlUtil.encodeURLComponent(""));
        // null
        Assert.assertEquals("", UrlUtil.encodeURLComponent(null));
    }

    @Test
    public void testIsValidPathComponent() {
        // 合法未编码
        Assert.assertTrue(UrlUtil.isValidPathComponent("abc-_.~"));
        // 合法已编码
        Assert.assertTrue(UrlUtil.isValidPathComponent("abc%20def"));
        // 合法混合
        Assert.assertTrue(UrlUtil.isValidPathComponent("/path/%E4%B8%AD%E6%96%87/file.txt"));
        // 非法字符
        Assert.assertFalse(UrlUtil.isValidPathComponent("abc def")); // 空格未编码
        Assert.assertFalse(UrlUtil.isValidPathComponent("abc%2Gdef")); // 非法百分号编码
        Assert.assertFalse(UrlUtil.isValidPathComponent("abc%def")); // 百分号后不足两位
        Assert.assertFalse(UrlUtil.isValidPathComponent("abc#def")); // #不允许
        // 边界
        Assert.assertTrue(UrlUtil.isValidPathComponent(""));
        Assert.assertFalse(UrlUtil.isValidPathComponent(null));
    }

    @Test
    public void testIsValidQueryComponent() {
        // 合法未编码
        Assert.assertTrue(UrlUtil.isValidQueryComponent("abc-_.~"));
        // 合法已编码
        Assert.assertTrue(UrlUtil.isValidQueryComponent("abc%20def"));
        // 合法混合
        Assert.assertTrue(UrlUtil.isValidQueryComponent("name=%E5%BC%A0%E4%B8%89&age=25"));
        // 非法字符
        Assert.assertFalse(UrlUtil.isValidQueryComponent("abc def")); // 空格未编码
        Assert.assertFalse(UrlUtil.isValidQueryComponent("abc%2Gdef")); // 非法百分号编码
        Assert.assertFalse(UrlUtil.isValidQueryComponent("abc%def")); // 百分号后不足两位
        Assert.assertFalse(UrlUtil.isValidQueryComponent("abc#def")); // #不允许
        // 边界
        Assert.assertTrue(UrlUtil.isValidQueryComponent(""));
        Assert.assertFalse(UrlUtil.isValidQueryComponent(null));
    }

    @Test
    public void testIsValidComponent_malformedPercentEncoding() {
        // 畸形百分号编码
        Assert.assertFalse("末尾是%", UrlUtil.isValidQueryComponent("abc%"));
        Assert.assertFalse("末尾只有一个字符", UrlUtil.isValidQueryComponent("abc%a"));
        Assert.assertFalse("末尾有非十六进制", UrlUtil.isValidQueryComponent("abc%2G"));
        Assert.assertFalse("混合非法序列", UrlUtil.isValidQueryComponent("a%20b%dec%45"));
        Assert.assertFalse("无效UTF-8序列 %de", UrlUtil.isValidQueryComponent("%de"));
    }

    @Test
    public void testControlCharacters() throws Exception {
        // 控制字符
        String urlWithControlChars = "https://example.com/path\twith\ncontrol?param=\r";
        String expected = "https://example.com/path%09with%0Acontrol?param=%0D";
        Assert.assertEquals(expected, UrlUtil.encodeURL(urlWithControlChars));
    }

    @Test
    public void testPathWithDotSegments() throws Exception {
        // 路径中的点
        String url = "https://example.com/./a/../b/";
        String expected = "https://example.com/./a/../b/";
        Assert.assertEquals("encodeURL不应该处理路径中的.", expected, UrlUtil.encodeURL(url));
    }
} 