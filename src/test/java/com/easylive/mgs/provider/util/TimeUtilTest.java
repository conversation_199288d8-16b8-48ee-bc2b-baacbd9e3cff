package com.easylive.mgs.provider.util;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalTime;
import java.util.List;

/**
 * TimeUtil工具类的单元测试
 * 
 * <AUTHOR>
 * @date 2025/3/21
 */
public class TimeUtilTest {
    private static final Logger logger = LoggerFactory.getLogger(TimeUtilTest.class);

    @Test
    public void testParseTimeRange_NormalTimeRanges() {
        // 测试正常的时间区间
        String timeStr = "20:00-21:00,15:00-17:00";
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(timeStr);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
        
        Assert.assertEquals(LocalTime.of(20, 0), result.get(0).getLeft());
        Assert.assertEquals(LocalTime.of(21, 0), result.get(0).getRight());
        
        Assert.assertEquals(LocalTime.of(15, 0), result.get(1).getLeft());
        Assert.assertEquals(LocalTime.of(17, 0), result.get(1).getRight());
        
        logger.info("正常时间区间测试通过");
    }

    @Test
    public void testParseTimeRange_OpenEndedRanges() {
        // 测试开放式结束时间
        String timeStr = "13:00-15:00, 20:00-";
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(timeStr);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
        
        Assert.assertEquals(LocalTime.of(13, 0), result.get(0).getLeft());
        Assert.assertEquals(LocalTime.of(15, 0), result.get(0).getRight());
        
        Assert.assertEquals(LocalTime.of(20, 0), result.get(1).getLeft());
        Assert.assertEquals(LocalTime.MAX, result.get(1).getRight());
        
        logger.info("开放式结束时间测试通过");
    }

    @Test
    public void testParseTimeRange_OpenStartRanges() {
        // 测试开放式开始时间
        String timeStr = "-18:00, 19:00-";
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(timeStr);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
        
        Assert.assertEquals(LocalTime.MIN, result.get(0).getLeft());
        Assert.assertEquals(LocalTime.of(18, 0), result.get(0).getRight());
        
        Assert.assertEquals(LocalTime.of(19, 0), result.get(1).getLeft());
        Assert.assertEquals(LocalTime.MAX, result.get(1).getRight());
        
        logger.info("开放式开始时间测试通过");
    }

    @Test
    public void testParseTimeRange_InvalidRanges() {
        // 测试无效的时间区间（结束时间早于开始时间）
        String timeStr = "21:00-20:00,17:00-15:00";
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(timeStr);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(0, result.size());
        
        logger.info("无效时间区间测试通过");
    }

    @Test
    public void testParseTimeRange_MixedValidAndInvalidRanges() {
        // 测试混合有效和无效的时间区间
        String timeStr = "21:00-20:00,15:00-17:00";
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(timeStr);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(LocalTime.of(15, 0), result.get(0).getLeft());
        Assert.assertEquals(LocalTime.of(17, 0), result.get(0).getRight());
        
        logger.info("混合有效和无效时间区间测试通过");
    }

    @Test
    public void testParseTimeRange_InvalidFormat() {
        // 测试格式无效的时间
        String timeStr = "abc-def,15:00-17:00";
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(timeStr);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(LocalTime.of(15, 0), result.get(0).getLeft());
        Assert.assertEquals(LocalTime.of(17, 0), result.get(0).getRight());
        
        logger.info("格式无效时间测试通过");
    }

    @Test
    public void testParseTimeRange_EmptyInput() {
        // 测试空输入
        String timeStr = "";
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(timeStr);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(0, result.size());
        
        logger.info("空输入测试通过");
    }

    @Test
    public void testParseTimeRange_NullInput() {
        // 测试null输入
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(null);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(0, result.size());
        
        logger.info("null输入测试通过");
    }
    
    @Test
    public void testParseTimeRange_MultipleRangesWithSpaces() {
        // 测试带有空格的多个时间区间
        String timeStr = " 08:30 - 12:00 , 14:30 - 18:00 ";
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(timeStr);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
        
        Assert.assertEquals(LocalTime.of(8, 30), result.get(0).getLeft());
        Assert.assertEquals(LocalTime.of(12, 0), result.get(0).getRight());
        
        Assert.assertEquals(LocalTime.of(14, 30), result.get(1).getLeft());
        Assert.assertEquals(LocalTime.of(18, 0), result.get(1).getRight());
        
        logger.info("带空格的多个时间区间测试通过");
    }
    
    @Test
    public void testParseTimeRange_EqualStartAndEndTime() {
        // 测试开始时间等于结束时间的情况
        String timeStr = "10:00-10:00";
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(timeStr);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        
        Assert.assertEquals(LocalTime.of(10, 0), result.get(0).getLeft());
        Assert.assertEquals(LocalTime.of(10, 0), result.get(0).getRight());
        
        logger.info("开始时间等于结束时间测试通过");
    }
    
    @Test
    public void testParseTimeRange_ThreeTimeRanges() {
        // 测试三个连续的时间区间
        String timeStr = "08:00-10:00,12:00-14:00,16:00-18:00";
        List<Pair<LocalTime, LocalTime>> result = com.easylive.mgs.provider.pub.util.TimeUtil.parseTimeRange(timeStr);
        
        Assert.assertNotNull(result);
        Assert.assertEquals(3, result.size());
        
        Assert.assertEquals(LocalTime.of(8, 0), result.get(0).getLeft());
        Assert.assertEquals(LocalTime.of(10, 0), result.get(0).getRight());
        
        Assert.assertEquals(LocalTime.of(12, 0), result.get(1).getLeft());
        Assert.assertEquals(LocalTime.of(14, 0), result.get(1).getRight());
        
        Assert.assertEquals(LocalTime.of(16, 0), result.get(2).getLeft());
        Assert.assertEquals(LocalTime.of(18, 0), result.get(2).getRight());
        
        logger.info("三个时间区间测试通过");
    }
}