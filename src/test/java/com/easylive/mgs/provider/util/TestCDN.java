package com.easylive.mgs.provider.util;

import com.easylive.common.util.SystemUtil;
import com.easylive.mgs.provider.enums.CDNRefreshStatus;
import com.easylive.mgs.provider.pub.cdn.URLSigner;
import com.easylive.mgs.provider.pub.cdn.URLSignerFactory;
import com.easylive.mgs.provider.pub.cdn.QiniuURLSignerFactory;
import com.easylive.mgs.provider.pub.enums.CDNDomainType;
import com.easylive.mgs.provider.pub.enums.CDNRefreshType;
import com.easylive.mgs.provider.pub.enums.CDNSignType;
import com.easylive.mgs.provider.pub.enums.CloudProviderType;
import com.easylive.mgs.provider.service.cdn.BaiduCDNProvider;
import com.easylive.mgs.provider.service.cdn.CDNRefreshTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
public class TestCDN {
    @Test
    public void testBaiduCDNSign() {
        String url = "/large/pgc-image/a9c4f7c4d4f94f0e9d4c4f9f9f9f9f9f9";
        String sign = URLSignerFactory.getCDNSigner(CloudProviderType.BAIDU, CDNSignType.C).sign("testkey", url, 1734587159L);
        Assert.assertEquals(sign, "/974a9435de2c565b1973fc7c2f9c6dcd/6763b317/large/pgc-image/a9c4f7c4d4f94f0e9d4c4f9f9f9f9f9f9");
        System.out.println(sign);
    }

    @Test
    public void testAli() {

    }

    @Test
    public void testTencentLive() {
        String url = "http://www.test.com/live/test01.flv";
        String key = "ngoeiq03";
        long expireTime = **********;
        String sign = URLSignerFactory.getSigner(CloudProviderType.TENCENT, CDNSignType.DEFAULT, CDNDomainType.PULL).sign(key, url, expireTime);
        System.out.println(sign);
        Assert.assertEquals("txSecret=ce797dc6238156d548ef945e6ad1ea20&txTime=5C01D608", sign);

        url = "rtmp://tlive.wuhanzhiyou.com/record/1yR8tNqBezEtrd";
        key = "FrFOEZ7vkm1msetG";
        expireTime = System.currentTimeMillis() /1000 + 3600;
        sign = URLSignerFactory.getSigner(CloudProviderType.TENCENT, CDNSignType.DEFAULT, CDNDomainType.PULL).sign(key, url, expireTime);
        System.out.println(url + "?" + sign);
    }

    @Test
    public void testHuoshanLive() {
        String path = "/live/1yR8tNqBezEtrd";
        String url = "rtmp://hlive.whzy1.com" + path;
        String key = "j5jYF19KvBlc4MDk";
        long expireTime = System.currentTimeMillis() /1000 + 3600;
        String sign = URLSignerFactory.getSigner(CloudProviderType.HUOSHAN, CDNSignType.DEFAULT, CDNDomainType.PULL).sign(key, path, expireTime);
        System.out.println(url + "?" + sign);
    }

    @Test
    public void testBaiduCDNRefresh() {
        String[] urls = new String[]{"http://thumb.whzy1.com/online/thumb/8/3f/4eTZzzV0uazZ-u.jpg"};
        BaiduCDNProvider baiduCDNProvider = new BaiduCDNProvider("", "");
        try {
            String result = baiduCDNProvider.refresh(urls, CDNRefreshType.FILE, true);
            log.info("TaskID: " + result);
            while( true ) {
                CDNRefreshTask task = baiduCDNProvider.checkStatus("thumb.whzy1.com", result);
                System.out.println(task);
                SystemUtil.sleep(2000);
                if(task.getStatus() == CDNRefreshStatus.COMPLETE) {
                    log.info("Refresh Success");
                    break;
                } else if(task.getStatus() == CDNRefreshStatus.FAILED) {
                    log.error("Refresh Failed");
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testQiniuCDNSign() {


        // 七牛云官方例子
        // 原始 url : http://xxx.yyy.com/DIR1/中文/vodfile.mp4?v=1.2
        // path: /DIR1/中文/vodfile.mp4
        // KEY: 9388f4ba63b89bba5b9b84aa70a92eaac099d39b
        // T: 55bb9b80
        // S = key + url_encode(path) + T
        // S = 9388f4ba63b89bba5b9b84aa70a92eaac099d39b/DIR1/%E4%B8%AD%E6%96%87/vodfile.mp455bb9b80
        // SIGN = b4b7f94dd7817ce0283b5491861c3936
        // 访问 url 为：
        // http://xxx.yyy.com/DIR1/%E4%B8%AD%E6%96%87/vodfile.mp4?v=1.2&sign=b4b7f94dd7817ce0283b5491861c3936&t=55bb9b80
        String path = "/DIR1/中文/vodfile.mp4?v=1.2";
        String key = "9388f4ba63b89bba5b9b84aa70a92eaac099d39b";
        long expireTime = Long.parseLong("55bb9b80", 16); // 1438419840L
        String expectedUrl = "/DIR1/%E4%B8%AD%E6%96%87/vodfile.mp4?v=1.2&sign=b4b7f94dd7817ce0283b5491861c3936&t=55bb9b80";
        String actualUrl = URLSignerFactory.getCDNSigner(CloudProviderType.QINIU, CDNSignType.DEFAULT).sign(key, path, expireTime);
        Assert.assertEquals(expectedUrl, actualUrl);
        System.out.println("Qiniu官方网页例子签名URL: " + actualUrl);
    }
}
