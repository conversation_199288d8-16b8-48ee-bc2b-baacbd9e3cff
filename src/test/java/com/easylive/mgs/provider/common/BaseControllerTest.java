package com.easylive.mgs.provider.common;

import org.hamcrest.Matcher;
import org.hamcrest.collection.IsMapContaining;
import org.hamcrest.core.Is;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.ResultHandler;
import org.springframework.test.web.servlet.ResultMatcher;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.result.StatusResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-06-06
 * Controller测试基类
 */

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class BaseControllerTest {
    @Autowired
    protected MockMvc mvc;

    @LocalServerPort
    int randomServerPort;

    protected String serviceUrl;

    @Autowired
    WebApplicationContext wc;

    public static final String BASE_NODE_CODE = "code";
    public static final String BASE_NODE_MESSAGE = "message";
    public static final String BASE_NODE_DATA = "data";

    @Before
    public void beforeSetUp() {
        this.mvc = MockMvcBuilders.webAppContextSetup(wc).build();
        this.serviceUrl = "http://localhost:" + randomServerPort;
    }

    @Test
    public void testEmpty() {

    }

    protected void commonTestGet(String url, MatcherBuilder builder) throws Exception {
        ResultActions actions = mvc.perform(get(url))
                .andExpect(status().isOk());

        actions = builder.build(actions);

        actions.andDo(print())
                .andReturn();
    }

    protected void commonTestGetBizError(String url, MatcherBuilder builder) throws Exception {
        ResultActions actions = mvc.perform(get(url))
                .andExpect(status().is(HttpStatus.UNPROCESSABLE_ENTITY.value()));

        actions = builder.build(actions);

        actions.andDo(print())
                .andReturn();
    }


    protected void commonTestPost(String url, MatcherBuilder builder, Object... uriVars) throws Exception {
        ResultActions actions = mvc.perform(post(url,uriVars))
                .andExpect(status().isOk());

        actions = builder.build(actions);

        actions.andDo(print())
                .andReturn();
    }

    protected <K, V> ResultMatcher commonJsonMapContains(K key, V value) {
        return jsonPath("$", jsonMapContains(key, value));
    }

    //以下为一些常用测试方法，让代码更加简洁

    protected ResultHandler print() {
        return MockMvcResultHandlers.print();
    }

    protected MockHttpServletRequestBuilder get(String url) {
        return MockMvcRequestBuilders.get(url);
    }

    protected MockHttpServletRequestBuilder get(String urlTemplate, Object... uriVars) {
        return MockMvcRequestBuilders.get(urlTemplate, uriVars);
    }

    protected MockHttpServletRequestBuilder post(String url) {
        return MockMvcRequestBuilders.post(url);
    }

    protected MockHttpServletRequestBuilder post(String urlTemplate, Object... uriVars) {
        return MockMvcRequestBuilders.post(urlTemplate, uriVars);
    }

    protected StatusResultMatchers status() {
        return MockMvcResultMatchers.status();
    }


    //json path相关的方法
    protected <T> ResultMatcher jsonPath(String expression, Matcher<T> matcher) {
        return MockMvcResultMatchers.jsonPath(expression, matcher);
    }

    protected <T> Matcher<T> jsonValueIs(T value) {
        return Is.is(value);
    }

    protected <K, V> Matcher<Map<? extends K, ? extends V>> jsonMapContains(K key, V value) {
        return IsMapContaining.hasEntry(key, value);
    }


}
